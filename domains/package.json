{"name": "@bika/domains", "description": "", "private": true, "dependencies": {"@ag-grid-community/client-side-row-model": "^32.2.0", "@ag-grid-community/core": "^32.2.0", "@ag-grid-community/infinite-row-model": "^32.2.0", "@ag-grid-community/react": "^32.2.0", "@ag-grid-community/styles": "^32.2.0", "@ag-grid-enterprise/advanced-filter": "^32.2.0", "@ag-grid-enterprise/clipboard": "^32.2.0", "@ag-grid-enterprise/column-tool-panel": "^32.2.0", "@ag-grid-enterprise/core": "^32.2.0", "@ag-grid-enterprise/excel-export": "32.2.0", "@ag-grid-enterprise/master-detail": "^32.2.0", "@ag-grid-enterprise/menu": "^32.2.0", "@ag-grid-enterprise/range-selection": "^32.2.0", "@ag-grid-enterprise/server-side-row-model": "^32.2.0", "@ag-grid-enterprise/set-filter": "^32.2.0", "@ag-grid-enterprise/viewport-row-model": "^32.2.0", "@ai-sdk/amazon-bedrock": "^2.2.10", "@ai-sdk/azure": "^1.3.23", "@ai-sdk/google": "^1.2.22", "@ai-sdk/openai": "^1.3.22", "@ant-design/nextjs-registry": "^1.0.0", "@aws-sdk/client-ses": "^3.609.0", "@bika.ai/bika-zapier": "workspace:*", "@bika.ai/license": "1.0.1", "@bika/api-caller": "workspace:*", "@bika/contents": "workspace:*", "@bika/domains": "workspace:*", "@bika/sdk-ts": "workspace:*", "@bika/server-orm": "workspace:*", "@bika/types": "workspace:*", "@bika/ui": "workspace:*", "@casl/ability": "^6.7.1", "@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@fingerprintjs/fingerprintjs": "^4.2.2", "@floating-ui/dom": "1.6.12", "@floating-ui/react": "0.26.28", "@fontsource/inter": "^5.0.16", "@formatjs/intl-localematcher": "^0.5.2", "@headlessui/react": "^1.7.17", "@hello-pangea/dnd": "^16.5.0", "@heroicons/react": "^2.1.1", "@hocuspocus/provider": "^2.15.0", "@hocuspocus/transformer": "^2.15.0", "@hono/zod-openapi": "^0.16.4", "@hookform/resolvers": "^3.3.4", "@langchain/community": "^0.3.32", "@langchain/core": "^0.3.40", "@langchain/openai": "^0.4.4", "@mdx-js/loader": "^3.0.0", "@mdx-js/react": "^3.0.0", "@mui/joy": "^5.0.0-beta.51", "@mui/x-date-pickers": "7.16.0", "@mui/x-license": "^8.9.2", "@mui/x-tree-view": "^8.9.2", "@mui/x-tree-view-pro": "^8.9.2", "@next/mdx": "15.2.3", "@next/third-parties": "^14.2.4", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-slot": "^1.0.2", "@react-email/render": "^0.0.12", "@rsql/ast": "^1.6.0", "@rsql/parser": "^1.6.0", "@sentry/node": "^7.108.0", "@tanstack/react-query": "^4.0.0", "@tavily/core": "^0.5.9", "@tiptap/html": "^2.8.0", "@toolsdk.ai/orm": "workspace:*", "@toolsdk.ai/sdk-ts": "workspace:*", "@trpc/client": "^10.45.2", "@types/ip": "^1.1.3", "@types/mdx": "^2.0.10", "@types/react-window": "^1.8.8", "@vercel/analytics": "^1.1.2", "@xyflow/react": "^12.0.2", "ag-grid-community": "^32.2.0", "ag-grid-react": "^32.2.0", "ahooks": "^3.7.10", "ai": "^4.3.19", "attr-accept": "^2.2.2", "axios": "^1.9.0", "axios-retry": "^4.5.0", "basenext": "workspace:*", "bika.ai": "workspace:*", "cardinal": "^2.1.1", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.0", "cron-parser": "^4.9.0", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "dayjs": "1.11.10", "echarts-for-react": "^3.0.2", "exa-js": "^1.8.20", "exceljs": "^4.4.0", "expo-server-sdk": "^3.7.0", "fast-xml-parser": "^4.4.0", "firebase": "^10.8.0", "firebase-admin": "^12.0.0", "gray-matter": "^4.0.3", "hono": "^4.6.12", "https-proxy-agent": "^7.0.5", "immer": "^10.0.3", "ip-address": "^9.0.5", "json-schema-to-zod": "^2.6.1", "jsonwebtoken": "^9.0.2", "langchain": "^0.3.19", "lodash": "^4.17.21", "lottie-web": "^5.12.2", "lucide-react": "^0.303.0", "mailparser": "^3.7.1", "marked": "^13.0.2", "mime-types": "^2.1.35", "motion": "^12.6.3", "mysql2": "^2.3.3", "negotiator": "^0.6.3", "next": "15.3.3", "next-mdx-remote": "^4.4.1", "node-cache": "^5.1.2", "nodemailer": "^6.9.8", "openai": "^4.85.4", "original-fs": "^1.2.0", "path-to-regexp": "^8.2.0", "pg": "^8.11.3", "pino": "^8.19.0", "pinyin-pro": "^3.19.3", "posthog-js": "^1.203.1", "qs": "^6.13.0", "rate-limiter-flexible": "^6.2.1", "react": "18.3.1", "react-dom": "18.3.1", "react-dropzone": "^14.2.3", "react-grid-layout": "^1.5.0", "react-hook-form": "^7.49.3", "react-hot-keys": "^2.7.3", "react-hot-toast": "^2.4.1", "react-image-crop": "^8.6.2", "react-resizable-panels": "^2.1.8", "react-speech-recognition": "^4.0.1", "react-use": "^17.5.0", "react-virtualized-auto-sizer": "^1.0.25", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "reactflow": "^11.11.3", "regenerator-runtime": "^0.14.1", "rehype-raw": "^7.0.0", "resend": "2.1.0", "resumable-stream": "^2.2.1", "safe-imap": "^0.1.2", "samlify": "^2.8.11", "samlify-validator-js": "^0.0.1", "semver": "^7.5.4", "server-only": "^0.0.1", "sharelib": "workspace:*", "sharp": "^0.34.0", "showdown": "^2.1.0", "tencentcloud-sdk-nodejs-sms": "^4.0.809", "toolsdk": "workspace:*", "tough-cookie": "^4.1.3", "ts-pattern": "5.0.6", "twitter-api-v2": "^1.18.2", "typeorm": "^0.3.20", "ua-parser-js": "^1.0.37", "unsplash-js": "^7.0.19", "yaml": "^2.4.5", "zod": "^3.24.5", "zod-to-json-schema": "^3.24.5", "zustand": "^5.0.1"}, "devDependencies": {"@mermaid-js/mermaid-cli": "^10.6.1", "@mui/x-date-pickers": "7.16.0", "@playwright/experimental-ct-react": "^1.40.0", "@playwright/test": "^1.40.1", "@testing-library/react": "^14.1.2", "@types/lodash": "^4.14.202", "@types/luxon": "^3.4.2", "@types/mailparser": "^3.4.5", "@types/mdx": "^2.0.10", "@types/mime-types": "^2.1.4", "@types/negotiator": "^0.6.3", "@types/netmask": "^2.0.5", "@types/node": "^20.11.24", "@types/node-cron": "^3.0.11", "@types/pg": "^8.11.0", "@types/pluralize": "^0.0.33", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-speech-recognition": "^3.9.6", "@types/semver": "^7.5.8", "@types/showdown": "^2.0.6", "@types/ua-parser-js": "^0.7.39", "@vikadata/vika": "^1.4.1", "@vitejs/plugin-react-swc": "^3.11.0", "@vitest/coverage-v8": "^3.2.4", "autoprefixer": "^10.4.16", "dotenv-cli": "^7.3.0", "eslint": "^8", "eslint-config-next": "15.3.3", "jsdom": "^23.0.1", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.7.2", "vite-tsconfig-paths": "^4.3.1", "vitest": "^3.2.4"}, "peerDependencies": {"@mui/material": "^5.15.11"}, "exports": {"./story-components/*": "./story-components/*.tsx", "./admin/apis": "./admin/apis/index.ts", "./admin/apis/*": "./admin/apis/*.tsx", "./admin/server/*": "./admin/server/*.ts", "./admin/client/*": "./admin/client/*.tsx", "./admin/types/*": "./admin/types/*.ts", "./event/server/*": "./event/server/*.ts", "./auth/client": "./auth/client/index.ts", "./auth/client/*": "./auth/client/*.tsx", "./auth/client/utils": "./auth/client/utils.ts", "./auth/client/styles/": "./auth/client/styles/", "./auth/client/components": "./auth/client/components/index.ts", "./auth/client/components/*": "./auth/client/components/*.tsx", "./auth/client/datepicker": "./auth/client/components/datepicker-component.tsx", "./automation/apis": "./automation/apis/index.ts", "./automation/client": "./automation/client/index.ts", "./automation/client/*": "./automation/client/*.tsx", "./automation/server": "./automation/server/index.ts", "./automation/server/*": "./automation/server/*.ts", "./automation/server/action": "./automation/server/action/index.ts", "./automation-nodes/*/server": "./automation-nodes/*/server.ts", "./automation-nodes/*/vo-renderer": "./automation-nodes/*/vo-renderer.tsx", "./automation-nodes/*/bo-input": "./automation-nodes/*/bo-input.tsx", "./system/client/*": "./system/client/*.tsx", "./system/server/event": "./system/server/event/index.ts", "./system/server": "./system/server/index.ts", "./system/server/*": "./system/server/*.ts", "./system/apis": "./system/apis/index.ts", "./system/apis/*": "./system/apis/*.ts", "./user/apis": "./user/apis/index.ts", "./user/server": "./user/server/index.ts", "./user/server/*": "./user/server/*.ts", "./user/client": "./user/client/index.ts", "./user/client/*": "./user/client/*.tsx", "./store/server/*": "./store/server/*.ts", "./space/apis": "./space/apis/index.ts", "./space/server": "./space/server/index.ts", "./space/server/*": "./space/server/*.ts", "./space/client/*": "./space/client/*.tsx", "./talk/apis/*": "./talk/apis/*.ts", "./talk/server/*": "./talk/server/*.ts", "./talk/client/*": "./talk/client/*.tsx", "./unit/apis": "./unit/apis/index.ts", "./unit/server": "./unit/server/index.ts", "./unit/server/*": "./unit/server/*.ts", "./unit/client": "./unit/client/index.ts", "./unit/client/*": "./unit/client/*.ts", "./node/apis": "./node/apis/index.ts", "./node/apis/*": "./node/apis/*.ts", "./node/client": "./node/client/index.ts", "./node/client/*": "./node/client/*.tsx", "./node/server": "./node/server/index.ts", "./node/server/*": "./node/server/*.ts", "./node/client/node-create-view": "./node/client/node-create-view/index.tsx", "./node-resources/*/server": "./node-resources/*/server.ts", "./node-resources/*/vo-renderer": "./node-resources/*/vo-renderer.tsx", "./node-resources/*/bo-input": "./node-resources/*/bo-input.tsx", "./openapi/apis": "./openapi/apis/index.ts", "./openapi/apis/*": "./openapi/apis/*.ts", "./openapi/server": "./openapi/server/index.ts", "./openapi/server/*": "./openapi/server/*.ts", "./openapi/types": "./openapi/types/index.ts", "./openapi/types/*": "./openapi/types/*.ts", "./database/apis": "./database/apis/index.ts", "./database/apis/*": "./database/apis/*.ts", "./database/client/*": "./database/client/*.tsx", "./database/client/form/interface": "./database/client/form/interface.ts", "./database/client/form/*": "./database/client/form/*.tsx", "./database/client/table-view/ag-grid/store": "./database/client/table-view/ag-grid/store/collaborationStore.ts", "./database/client/table-view/ag-grid/types": "./database/client/table-view/ag-grid/types.ts", "./database/client/table-view/ag-grid": "./database/client/table-view/ag-grid/index.ts", "./database/server": "./database/server/index.ts", "./database/server/*": "./database/server/*.ts", "./database/shared": "./database/shared/index.ts", "./database-views/*/server": "./database-views/*/server.ts", "./database-views/*/vo-renderer": "./database-views/*/vo-renderer.tsx", "./database-views/*/bo-input": "./database-views/*/bo-input.tsx", "./database-fields/*/server": "./database-fields/*/server.ts", "./database-fields/*/vo-renderer": "./database-fields/*/vo-renderer.tsx", "./database-fields/*/bo-input": "./database-fields/*/bo-input.tsx", "./database-fields/*/cell-editor": "./database-fields/*/cell-editor.tsx", "./database-fields/*/cell-renderer": "./database-fields/*/cell-renderer.tsx", "./database-fields/*/ag-grid-cell-renderer": "./database-fields/*/ag-grid-cell-renderer.tsx", "./database-fields/*/ag-grid-cell-editor": "./database-fields/*/ag-grid-cell-editor.tsx", "./doc/server/*": "./doc/server/*.ts", "./dashboard/client": "./dashboard/client/index.ts", "./dashboard/client/*": "./dashboard/client/*.tsx", "./dashboard/apis": "./dashboard/apis/index.ts", "./dashboard/server": "./dashboard/server/index.ts", "./dashboard/server/*": "./dashboard/server/*.ts", "./dashboard-widgets/server-registry": "./dashboard-widgets/server-registry.ts", "./dashboard-widgets/client-registry": "./dashboard-widgets/client-registry.tsx", "./dashboard-widgets/*/server": "./dashboard-widgets/*/server.ts", "./dashboard-widgets/*/vo-renderer": "./dashboard-widgets/*/vo-renderer.tsx", "./dashboard-widgets/*/bo-input": "./dashboard-widgets/*/bo-input.tsx", "./email/api": "./email/api/index.ts", "./email/api/*": "./email/api/*.ts", "./integration/client": "./integration/client/index.ts", "./integration/client/*": "./integration/client/*.tsx", "./integration/server": "./integration/server/index.ts", "./integration/server/*": "./integration/server/*.ts", "./integration/apis": "./integration/apis/index.ts", "./integration/apis/*": "./integration/apis/*.ts", "./template/apis": "./template/apis/index.ts", "./template/apis/*": "./template/apis/*.ts", "./template/server": "./template/server/index.ts", "./template/server/*": "./template/server/*.ts", "./template/client": "./template/client/index.ts", "./template/client/*": "./template/client/*.tsx", "./editor/client/*": "./editor/client/*.tsx", "./mission/apis": "./mission/apis/index.ts", "./mission/client": "./mission/client/index.ts", "./mission/client/*": "./mission/client/*.tsx", "./mission/server": "./mission/server/index.ts", "./mission/server/*": "./mission/server/*.ts", "./mission/server/handlers": "./mission/server/handlers/index.ts", "./campaign/server": "./campaign/server/index.ts", "./campaign/server/*": "./campaign/server/*.ts", "./notification/apis": "./notification/api/index.ts", "./notification/server": "./notification/server/index.ts", "./notification/server/*": "./notification/server/*.ts", "./permission/server": "./permission/server/index.ts", "./permission/server/*": "./permission/server/*.ts", "./reminder/apis": "./reminder/apis/index.ts", "./reminder/server": "./reminder/server/index.ts", "./reminder/server/*": "./reminder/server/*.ts", "./auth/apis": "./auth/apis/index.ts", "./auth/apis/*": "./auth/apis/*.ts", "./auth/server": "./auth/server/index.ts", "./auth/server/*": "./auth/server/*.ts", "./report/client": "./report/client/index.ts", "./report/client/*": "./report/client/*.tsx", "./report/apis": "./report/apis/index.ts", "./report/server": "./report/server/index.ts", "./report/server/*": "./report/server/*.ts", "./search/server/*": "./search/server/*.ts", "./email/server/imap": "./email/server/imap/index.ts", "./email/server/*": "./email/server/*.tsx", "./email/server": "./email/server/index.ts", "./email-providers/*/server": "./email-providers/*/server.ts", "./email-providers/*/client": "./email-providers/*/client.ts", "./email-providers/*": "./email-providers/*.ts", "./edge/server": "./edge/server/index.ts", "./edge/server/*": "./edge/server/*.ts", "./edge/apis": "./edge/apis/index.ts", "./shared": "./shared/shared/index.ts", "./shared/*": "./shared/shared/*.ts", "./shared/client": "./shared/client/index.ts", "./shared/client/context": "./shared/client/context/index.ts", "./shared/client/*": "./shared/client/*.tsx", "./shared/client/utils": "./shared/client/utils.ts", "./shared/client/styles/": "./shared/client/styles/", "./shared/client/auth": "./shared/client/auth/index.ts", "./shared/client/components": "./shared/client/components/index.ts", "./shared/client/error-handler": "./shared/client/error-handler/index.ts", "./shared/client/components/*": "./shared/client/components/*.tsx", "./shared/client/datepicker": "./shared/client/components/datepicker-component.tsx", "./shared/server": "./shared/server/index.ts", "./shared/server/*": "./shared/server/*.ts", "./shared/shared": "./shared/shared/index.ts", "./shared/ui": "./shared/ui/index.ts", "./attachment/server": "./attachment/server/index.ts", "./attachment/apis": "./attachment/apis/index.ts", "./attachment/server/*": "./attachment/server/*.ts", "./attachment/client/*": "./attachment/client/*.tsx", "./form/client": "./form/client/index.ts", "./form/client/*": "./form/client/*.tsx", "./form/server/*": "./form/server/*.ts", "./form/server": "./form/server/index.ts", "./form/apis/*": "./form/apis/*.ts", "./form/apis": "./form/apis/index.ts", "./doc/client/*": "./doc/client/*.tsx", "./ai/mobile": "./ai/mobile/index.ts", "./ai/apis": "./ai/apis/index.ts", "./ai/server": "./ai/server/index.ts", "./ai/server/*": "./ai/server/*.ts", "./ai/server/integration/*": "./ai/server/integration/*.ts", "./ai/web": "./ai/web/index.ts", "./ai/client/build": "./ai/client/build/index.ts", "./ai/client/wizard/ai-wizard-view": "./ai/client/wizard/ai-wizard-view/index.tsx", "./ai/client": "./ai/client/index.ts", "./ai/client/chat/*": "./ai/client/chat/*.tsx", "./ai/client/*": "./ai/client/*.tsx", "./ai/types": "./ai/types/index.ts", "./ai/types/*": "./ai/types/*.ts", "./ai-intents/*": "./ai-intents/*.ts", "./ai-artifacts": "./ai-artifacts/index.ts", "./ai-artifacts/*": "./ai-artifacts/*.ts", "./ai-artifacts/*.tsx": "./ai-artifacts/*.tsx", "./website/client/*": "./website/client/*.tsx", "./website-global-modals/*": "./website-global-modals/*.tsx", "./website/client/context": "./website/client/context/index.ts", "./website/server": "./website/server/index.ts", "./website/server/*": "./website/server/*.ts", "./pricing/apis": "./pricing/apis/index.ts", "./pricing/server": "./pricing/server/index.ts", "./pricing/server/billing": "./pricing/server/billing/index.ts", "./pricing/server/*": "./pricing/server/*.ts", "./pricing/client": "./pricing/client/index.ts", "./pricing/client/*": "./pricing/client/*.tsx", "./mirror/client/*": "./mirror/client/*.tsx", "./mirror/server/*": "./mirror/server/*.ts", "./pricing/ui": "./pricing/ui/index.ts", "./pricing/ui/*": "./pricing/ui/*.tsx", "./change/apis": "./change/apis/index.ts", "./change/apis/*": "./change/apis/*.ts", "./change/client/*": "./change/client/*.tsx", "./change/config/*": "./change/config/*.ts", "./change/server/*": "./change/server/*.ts", "./ai-skillset/server-registry": "./ai-skillset/server-registry.ts", "./ai-skillset/client-registry": "./ai-skillset/client-registry.tsx", "./ai-skillset/*": "./ai-skillset/*.ts", "./__tests__/*": "./__tests__/*.ts", "./__tests__/mock": "./__tests__/mock/index.ts"}, "scripts": {"check": "NODE_OPTIONS=--max-old-space-size=8192 tsc --noEmit", "check-node": "node --max_old_space_size=8192 $(which tsc) --noEmit", "lint": "eslint --quiet --fix --ext js,ts,tsx .", "test": "dotenv -e ../apps/web/.env.local -- vitest", "test:concurrent": "dotenv -e ../apps/web/.env.local -- vitest --run --reporter=verbose --pool=threads --poolOptions.threads.maxThreads=4", "benchmark": "dotenv -e ../apps/web/.env.local -- vitest bench", "test:unit": "dotenv -e ../apps/web/.env.local -- vitest --dir unit", "test:node": "dotenv -e ../apps/web/.env.local -- vitest --dir node", "test:openapi": "dotenv -e ../apps/web/.env.local -- vitest --dir openapi", "test-bun": "dotenv -e ../apps/web/.env.local -- bun test", "test:ai": "dotenv -e ../apps/web/.env.local -- vitest --dir ai", "test:edge": "dotenv -e ../apps/web/.env.local -- vitest --dir edge", "test:attachment": "dotenv -e ../apps/web/.env.local -- vitest --dir attachment", "test:template": "dotenv -e ../apps/web/.env.local -- vitest --dir template", "test:automation": "dotenv -e ../apps/web/.env.local -- vitest --dir automation", "test:billing": "dotenv -e ../apps/web/.env.local -- vitest --dir pricing", "test:content": "dotenv -e ../apps/web/.env.local -- vitest content", "test:storybook": "dotenv -e ../apps/web/.env.local -- vitest storybook", "test:remote-storage": "dotenv -e ../apps/web/.env.local -- vitest remote-storage", "test:database": "dotenv -e ../apps/web/.env.local -- vitest --dir database", "test:task": "dotenv -e ../apps/web/.env.local -- vitest task", "test:influencer": "dotenv -e ../apps/web/.env.local -- vitest influencer", "test:scheduler": "dotenv -e ../apps/web/.env.local -- vitest scheduler", "test:intg": "dotenv -e ../apps/web/.env.local -- vitest --dir integration", "test:lib": "dotenv -e ../apps/web/.env.local -- vitest lib", "test:minio": "dotenv -e ../apps/web/.env.local -- vitest minio", "test:mission": "dotenv -e ../apps/web/.env.local -- vitest --dir mission", "test:notification": "dotenv -e ../apps/web/.env.local -- vitest --dir notification", "test:permission": "dotenv -e ../apps/web/.env.local -- vitest --dir permission", "test:trigger": "dotenv -e ../apps/web/.env.local -- vitest trigger", "test:user": "dotenv -e ../apps/web/.env.local -- vitest --dir user", "test:reminder": "dotenv -e ../apps/web/.env.local -- vitest --dir reminder", "test:system": "dotenv -e ../apps/web/.env.local -- vitest --dir system", "test:store": "dotenv -e ../apps/web/.env.local -- vitest --dir store", "test:admin": "dotenv -e ../apps/web/.env.local -- vitest --dir admin", "test:form": "dotenv -e ../apps/web/.env.local -- vitest --dir form"}, "keywords": [], "author": "", "license": "ISC", "version": "1.9.2-beta.1"}