import dayjs, { type QUnitType } from 'dayjs';
import React from 'react';
import { useTRPCQuery } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n/context';
import { useSpaceContextForce } from '@bika/types/space/context';
import { useGlobalContext } from '@bika/types/website/context';
import { Button, IconButton } from '@bika/ui/button';
import { List, ListItem, ListItemDecorator, ListItemContent } from '@bika/ui/forms';
import CopyOutlined from '@bika/ui/icons/components/copy_outlined';
import DeleteOutlined from '@bika/ui/icons/components/delete_outlined';
import QuestionCircleOutlined from '@bika/ui/icons/components/question_circle_outlined';
import { Box, Stack } from '@bika/ui/layouts';
import { Modal } from '@bika/ui/modal';
import { SelectInput } from '@bika/ui/shared/types-form/select-input';
import { Skeleton } from '@bika/ui/skeleton';
import { snackbarShow } from '@bika/ui/snackbar';
import { Typography } from '@bika/ui/texts';
import { Tooltip } from '@bika/ui/tooltip';
import { copyText } from '@bika/ui/utils';
import { NavHeader } from '@bika/ui/web-layout';

const INDEFINITE = 'INDEFINITE';

function maskString(str: string, length = 6) {
  if (typeof str !== 'string' || str.length <= length) {
    return str;
  }

  const maskedLength = str.length - length;
  return '*'.repeat(maskedLength) + str.slice(-length);
}

const getISOString = (diff: string) => {
  const [numStr, unit] = diff.split(' ');
  return dayjs()
    .add(Number.parseInt(numStr, 10), unit as QUnitType)
    .toISOString();
};

interface CreateDeveloperTokenType {
  expirationDate: string;
}

export function UserSettingsPanelDeveloper() {
  const locale = useLocale();
  const spaceCtx = useSpaceContextForce();
  const globalContext = useGlobalContext();
  const trpcQuery = useTRPCQuery();
  const utils = trpcQuery.useUtils();
  const { data: devTokens, isLoading: isLoadingDevTokens } = trpcQuery.user.getDeveloperTokens.useQuery();
  const createDeveloperToken = trpcQuery.user.createDeveloperToken.useMutation();
  const deleteDeveloperToken = trpcQuery.user.deleteDeveloperToken.useMutation();
  const { mutateAsync: remoteCreateLicense } = trpcQuery.user.createLicenseKey.useMutation();
  const { data: licenseKey, refetch: fetchLicenseKey } = trpcQuery.user.getLicenseKey.useQuery(undefined, {
    enabled: globalContext.appEnv !== 'SELF-HOSTED',
  });

  const hasLicenseKey = Boolean(licenseKey);

  const [value, setValue] = React.useState<CreateDeveloperTokenType>({
    expirationDate: '7 days',
  });

  const { t } = locale;

  const handleCopy = async (token: string) => {
    if (token) {
      await copyText(token);
      snackbarShow({
        content: t.copy.copy_success,
        color: 'success',
      });
    }
  };

  const gotoLicenseSurvey = async () => {
    window.open('/license', '_blank');
  };

  if (isLoadingDevTokens)
    return (
      <>
        <NavHeader onClose={() => spaceCtx.showUIModal(null)}>{t.settings.account.api}</NavHeader>
        <Skeleton pos="USER_SETTINGS" />
      </>
    );

  return (
    <Box sx={{ px: 2 }}>
      <NavHeader onClose={() => spaceCtx.showUIModal(null)}>{t.settings.account.api}</NavHeader>
      <Typography level="h6">{t.api.create_token}</Typography>
      <Typography textColor="var(--text-secondary)">{t.api.create_token_description}</Typography>
      <Box display="flex">
        <Box width="200px" mr={3}>
          <SelectInput
            label={t.api.expiration}
            required
            placeholder={t.api.select_expiration}
            options={[
              {
                label: t.api.e1day,
                value: '1 day',
              },
              {
                label: t.api.e3days,
                value: '3 days',
              },
              {
                label: t.api.e7days,
                value: '7 days',
              },
              {
                label: t.api.e1month,
                value: '1 month',
              },
              {
                label: t.api.e2month,
                value: '2 month',
              },
              {
                label: t.api.e6month,
                value: '6 month',
              },
              {
                label: t.api.e1year,
                value: '1 year',
              },
              {
                label: t.api.never,
                value: INDEFINITE,
              },
            ]}
            value={value.expirationDate}
            onChange={(newValue) => setValue({ expirationDate: newValue! })}
          />
        </Box>
        <Button
          sx={{
            alignSelf: 'flex-end',
            height: '40px',
          }}
          onClick={() => {
            createDeveloperToken.mutate(
              {
                expirationDate: value.expirationDate === INDEFINITE ? undefined : getISOString(value.expirationDate),
              },
              {
                onSuccess: () => {
                  utils.user.getDeveloperTokens.invalidate();
                  snackbarShow({
                    content: t.api.create_token_success,
                    color: 'success',
                  });
                },
              },
            );
          }}
        >
          {t.buttons.create}
        </Button>
      </Box>
      <Typography level="h6" mt={2}>
        {t.api.my_tokens}
      </Typography>
      <Typography textColor="var(--text-secondary)">{t.api.my_tokens_description}</Typography>
      {devTokens && (
        <List sx={{ mt: 1 }}>
          {devTokens.map((token) => (
            <ListItem
              key={token.token}
              variant="soft"
              sx={{ mb: 1, height: '64px', borderRadius: '4px' }}
              endAction={
                <Stack direction="row" spacing={1}>
                  <Tooltip title={t.copy.copy} placement="top" arrow>
                    <IconButton onClick={() => handleCopy(token.token)}>
                      <CopyOutlined />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title={t.delete.delete} placement="top" arrow>
                    <IconButton
                      onClick={() => {
                        Modal.show({
                          type: 'error',
                          title: t.api.delete_token,
                          content: t.api.delete_token_description,
                          okText: t.action.ok,
                          cancelText: t.action.cancel,
                          onOk: async () => {
                            await deleteDeveloperToken.mutateAsync(token.token);
                            await utils.user.getDeveloperTokens.invalidate();
                            snackbarShow({
                              content: t.api.delete_token_success,
                              color: 'success',
                            });
                          },
                        });
                      }}
                    >
                      <DeleteOutlined />
                    </IconButton>
                  </Tooltip>
                </Stack>
              }
            >
              <ListItemDecorator>
                <ListItemContent>
                  <Typography>
                    {t.api.expiration}:
                    {token.expiration ? dayjs(token.expiration).format('YYYY-MM-DD HH:mm:ss') : t.api.never}
                  </Typography>
                  <Typography level="body-xs" textColor={'var(--text-secondary)'}>
                    {maskString(token.token)}
                  </Typography>
                </ListItemContent>
              </ListItemDecorator>
            </ListItem>
          ))}
        </List>
      )}
      {globalContext.appEnv !== 'SELF-HOSTED' && (
        <div>
          <div className="flex space-x-1 items-center mt-2">
            <Typography level="h6">{t.license.get_self_host_license_key}</Typography>
            <span
              className="cursor-pointer"
              onClick={() => {
                window.open('/license', '_blank');
              }}
            >
              <QuestionCircleOutlined color={'var(--text-secondary)'} />
            </span>
          </div>

          {hasLicenseKey ? (
            <Typography textColor="var(--text-secondary)">
              {t.license.trial_desc}，{t.license.please}&nbsp;
              <span
                className="text-[--brand] cursor-pointer"
                onClick={() => {
                  globalContext.showUIModal({
                    name: 'CONTACT_SERVICE',
                  });
                }}
              >
                {t.website.contact_service}
              </span>
            </Typography>
          ) : (
            <Typography textColor="var(--text-secondary)">{t.license.get_trial}</Typography>
          )}

          <div className="mt-2">
            {licenseKey ? (
              <List sx={{ mt: 1 }}>
                <ListItem
                  variant="soft"
                  sx={{ mb: 1, height: '64px', borderRadius: '4px' }}
                  endAction={
                    <Stack direction="row" spacing={1}>
                      <Tooltip title={t.copy.copy} placement="top" arrow>
                        <IconButton onClick={() => handleCopy(licenseKey.licenseKey)}>
                          <CopyOutlined />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title={t.delete.delete} placement="top" arrow>
                        <IconButton
                          onClick={() => {
                            Modal.show({
                              type: 'error',
                              title: t.api.delete_token,
                              content: t.api.delete_token_description,
                              okText: t.action.ok,
                              cancelText: t.action.cancel,
                              onOk: async () => {
                                // await deleteDeveloperToken.mutateAsync(token.token);
                                // await utils.user.getDeveloperTokens.invalidate();

                                // TODO: deletet self-hosted license key
                                await fetchLicenseKey();
                                snackbarShow({
                                  content: t.api.delete_token_success,
                                  color: 'success',
                                });
                              },
                            });
                          }}
                        >
                          <DeleteOutlined />
                        </IconButton>
                      </Tooltip>
                    </Stack>
                  }
                >
                  <ListItemDecorator>
                    <ListItemContent>
                      <Typography>
                        {t.api.expiration}:
                        {licenseKey.expiredAt ? dayjs(licenseKey.expiredAt).format('YYYY-MM-DD HH:mm:ss') : t.api.never}
                      </Typography>
                      <Typography level="body-xs" textColor={'var(--text-secondary)'} overflow={'auto'} width={'95%'}>
                        {licenseKey.licenseKey}
                      </Typography>
                    </ListItemContent>
                  </ListItemDecorator>
                </ListItem>
              </List>
            ) : (
              <></>
            )}

            <Button onClick={gotoLicenseSurvey}>{t.license.download_and_install_self_host}</Button>

            {/* 生产环境不出现创建，Self-hosted 更不出现 */}
            {globalContext.appEnv !== 'PRODUCTION' && (
              <Button
                onClick={async () => {
                  await remoteCreateLicense();
                  await fetchLicenseKey();
                }}
                sx={{
                  ml: 2,
                }}
              >
                Create a Self-hosted license key (beta)
              </Button>
            )}
          </div>
        </div>
      )}
    </Box>
  );
}
