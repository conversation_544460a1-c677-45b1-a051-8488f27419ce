/* eslint-disable max-classes-per-file */
import assert from 'assert';
import { generateNanoID } from 'basenext/utils/nano-id';
import Excel from 'exceljs';
import { errors } from '@bika/contents/config/server/error/errors';
import { ServerError } from '@bika/contents/config/server/error/server_error';
import { CellValueConvertorFactory } from '@bika/domains/database/server/cells/cellvalue-convertor/factory';
import {
  Database,
  DatabaseField,
  DatabaseFieldWithId,
  DatabaseRecord,
  View,
  ViewField,
  CellValue,
  RecordData,
} from '@bika/types/database/bo';
import { iStringParse, LocaleType } from '@bika/types/system';

export type ExcelField = DatabaseFieldWithId & ViewField;

export class ExcelImporter {
  public static generateIncrementalTemplate(sheet: Excel.Worksheet, fields: ExcelField[], localeType: LocaleType) {
    // TODO: 如字段的值是自动生成的，无需放置导入模板中
    ExcelImporter.setSheetHeader(sheet, fields, localeType);

    const { success } = ExcelImporter.validateSheetHeader(sheet, fields, localeType);
    assert(success, 'Excel template generation failed');
    // TODO: 生成几行模板数据
  }

  /**
   * 检查 Excel 的 Sheet 是否符合指定的 Header
   * 需要保证 Sheet 的第一行是 Header
   * 且 Header 与 fields 中的字段名位置一致
   */
  public static validateSheetHeader(
    sheet: Excel.Worksheet,
    fields: DatabaseField[],
    _locale: LocaleType,
  ): {
    success: boolean;
    reason?: {
      excelField: string;
      field: DatabaseField;
      columnKey: string;
    };
  } {
    if (!fields.length) {
      return { success: false };
    }

    // TODO: 自动识别 Header 的语言
    const header = sheet.getRow(1);

    for (let i = 0; i < fields.length; i++) {
      const field = fields[i];
      const excelField = header.getCell(i + 1).text;

      const fieldNames = typeof field.name === 'string' ? [field.name] : Object.values(field.name);

      if (!fieldNames.includes(excelField)) {
        return {
          success: false,
          reason: {
            excelField,
            field,
            columnKey: header.getCell(i + 1).address,
          },
        };
      }
    }

    return { success: true };
  }

  /**
   * 将View+Records转换成Excel文件
   */
  public static exportToWorksheet(
    sheet: Excel.Worksheet,
    fields: ExcelField[],
    records: DatabaseRecord[],
    localeType: LocaleType,
  ) {
    if (!fields) {
      return;
    }

    // 生成 Header
    ExcelImporter.setSheetHeader(sheet, fields, localeType);

    // 填充 Rows
    for (const record of records) {
      const row: Record<string, Excel.CellValue> = {};

      for (const field of fields) {
        const cvt = CellValueConvertorFactory.create(field, { fields });
        row[field.id] = cvt.cellValueToExcelCellValue(record.data[field.id] ?? null);
      }

      sheet.addRow(row);
    }
  }

  /**
   * 将 Excel 的 Sheet 转换成 DatabaseBO
   * 所有字段都使用 LongText 类型
   *
   * 1. 读取 Sheet 的第一行，作为 Header（字段名）
   * 2. 读取 Sheet 的第二行开始，作为数据行
   *
   * @param sheet Excel sheet
   */
  public static toDatabaseBO(resourceName: string, sheet: Excel.Worksheet): Database {
    // 读取第一行，作为 Header
    const header = sheet.getRow(1);
    const fields: DatabaseFieldWithId[] = [];
    const fieldMap = new Map<number, DatabaseFieldWithId>();
    header.eachCell((cell, colNum) => {
      // 忽略空 cell
      if (!cell.text) {
        return;
      }

      const field: DatabaseFieldWithId = {
        id: generateNanoID('fld'),
        name: cell.text,
        type: 'LONG_TEXT',
      };

      fields.push(field);
      fieldMap.set(colNum, field);
    });

    // 从第二行开始，作为数据行
    // 因为默认排序是按照objectId:-1，所以需要从最后一行开始读取
    const records: DatabaseRecord[] = [];
    for (let i = sheet.rowCount; i >= 2; i--) {
      const row = sheet.getRow(i);

      const record: DatabaseRecord = { data: {}, values: {} };
      row.eachCell((cell, colNum) => {
        let field = fieldMap.get(colNum);
        // add default field
        if (!field) {
          const emptyField: DatabaseFieldWithId = {
            id: generateNanoID('fld'),
            name: `field_${colNum}`,
            type: 'LONG_TEXT',
          };
          fields.push(emptyField);
          fieldMap.set(colNum, emptyField);
          field = emptyField;
        }

        // Excel 单元格类型解析
        if (cell.type === Excel.ValueType.Date && cell.value instanceof Date) {
          const data = cell.value.toISOString();
          record.data[field.id] = data;
          record.values![field.id] = data;
          return;
        }
        if (cell.type === Excel.ValueType.Boolean) {
          const data = cell.value ? 'true' : 'false';
          record.data[field.id] = data;
          record.values![field.id] = data;
          return;
        }
        if (cell.value) {
          // 默认使用文本
          record.data[field.id] = cell.text;
          record.values![field.id] = cell.text;
        } else {
          // 空单元格
          record.data[field.id] = '';
          record.values![field.id] = '';
        }
      });

      records.push(record);
    }

    // 生成 DatabaseBO
    return {
      resourceType: 'DATABASE',
      databaseType: 'DATUM',
      name: resourceName,
      fields,
      records,
    };
  }

  /**
   * 将 fields 转换成 ExcelFields
   * 如果指定了 view，则基于 view 中的 fields 进行转换
   */
  public static convertToExcelFields(fields: DatabaseFieldWithId[], view?: View): ExcelField[] {
    if (!view) {
      return fields;
    }

    if (!view.fields) {
      return [];
    }

    return view.fields.map((viewField) => {
      const field = fields.find((f) => f.id === viewField.id) as DatabaseFieldWithId;
      assert(field, `Field not found: ${viewField.id}`);
      assert(field.id, `Field ID cannot be null: ${viewField.id}`);
      return { ...field, ...viewField };
    });
  }

  private static setSheetHeader(
    /* eslint-disable no-param-reassign */
    sheet: Excel.Worksheet,
    fields: ExcelField[],
    localeType: LocaleType,
  ) {
    sheet.columns = fields.map((field) => ({
      header: iStringParse(field.name, localeType),
      key: field.id,
      width: field.width ?? 20,
    }));
  }
}

type SheetHeader = { field: DatabaseFieldWithId; columnIndex: number | null }[];

type RowConvertOptions = {
  // 当某字段转换失败，填充 undefined，否则抛出运行时异常
  // 默认为 true
  fillUndefinedOnError?: boolean;
};

export class ValidatedExcelSheet {
  private readonly _sheet: Excel.Worksheet;

  private readonly _fields: DatabaseFieldWithId[];

  private readonly _sheetHeader: SheetHeader;

  /**
   * 总行数
   * 从第二行开始计算，第一行是 Header
   */
  public get totalRows(): number {
    return this._sheet.rowCount - 1;
  }

  public static createAndValidate(
    sheet: Excel.Worksheet,
    fields: DatabaseFieldWithId[],
    locale: LocaleType,
  ): ValidatedExcelSheet {
    const sheetHeader = ValidatedExcelSheet.parseSheetHeader(sheet, fields, locale);
    return new ValidatedExcelSheet(sheet, fields, sheetHeader);
  }

  public static parseSheetHeader(
    sheet: Excel.Worksheet,
    fields: DatabaseFieldWithId[],
    locale: LocaleType,
  ): SheetHeader {
    if (!fields.length) {
      throw new ServerError(errors.database.excel_header_no_matching_fields);
    }

    // 构建 fieldName -> field 的映射
    const fieldMap = new Map<string /* fieldName */, DatabaseFieldWithId>();
    for (const field of fields) {
      fieldMap.set(iStringParse(field.name, locale), field);
    }

    const sheetHeader: SheetHeader = [];

    // 读取 Header 的每一列，构建 fieldId -> columnIndex 的映射
    const headerRow = sheet.getRow(1);
    headerRow.eachCell((cell, colNum) => {
      const fieldName = cell.text;
      const field = fieldMap.get(fieldName);

      // 忽略没有匹配到的字段
      if (!field) {
        return;
      }

      sheetHeader.push({ field, columnIndex: colNum });
    });

    if (sheetHeader.length === 0) {
      throw new ServerError(errors.database.excel_header_no_matching_fields);
    }

    // 追加没有匹配到的字段
    for (const field of fields) {
      if (!sheetHeader.some((h) => h.field.id === field.id)) {
        sheetHeader.push({ field, columnIndex: null });
      }
    }

    assert(sheetHeader.length === fields.length, 'Header has no matching fields');
    return sheetHeader;
  }

  private constructor(sheet: Excel.Worksheet, fields: DatabaseFieldWithId[], sheetHeader: SheetHeader) {
    this._sheet = sheet;
    this._fields = fields;
    this._sheetHeader = sheetHeader;
  }

  /**
   * 创建一个流，用于将 Excel 的 Sheet 的某些行转换成 Records
   *
   * @param batchSize 每次读取的行数
   * @param options 转换选项
   * @returns
   */
  public *createConvertStream(batchSize: number, options?: RowConvertOptions): Generator<RecordData[]> {
    for (let i = 0; i < this.totalRows; i += batchSize) {
      yield this.batchConvertRowsToRecords(i, batchSize, options);
    }
  }

  /**
   * 将 Excel 的 Sheet 的某些行转换成 Records
   *
   * @param offset 偏移量，从 0 开始
   * @param limit 读取的行数
   * @param options 转换选项
   * @returns
   */
  public batchConvertRowsToRecords(offset: number, limit: number, options?: RowConvertOptions): RecordData[] {
    const records: RecordData[] = [];

    const end = Math.min(offset + limit, this.totalRows);
    for (let i = offset; i < end; i++) {
      records.push(this.convertRowToRecord(i, options));
    }

    return records;
  }

  /**
   * 将 Excel 的 Sheet 的某一行转换成 Record
   *
   * @param sheetRowIndex 从 0 开始
   * @param options 转换选项
   * @returns
   */
  public convertRowToRecord(sheetRowIndex: number, options?: RowConvertOptions): Record<string, CellValue> {
    const { fillUndefinedOnError = true } = options ?? {};

    // 从第二行开始，第一行是 Header
    const row = this._sheet.getRow(sheetRowIndex + 2);

    const record: Record<string, CellValue> = {};

    for (const { field, columnIndex } of this._sheetHeader) {
      // 忽略没有匹配到的字段
      if (columnIndex === null) {
        continue;
      }

      const cell = row.getCell(columnIndex);
      const cvt = CellValueConvertorFactory.create(field, { fields: this._fields });

      let cellData: CellValue;
      try {
        cellData = cvt.excelCellToCellValue(cell);
      } catch (e) {
        if (!fillUndefinedOnError) {
          throw e;
        }
        console.error(`Field ${field.id} conversion failed: ${e}, excelText: ${cell.text}`);

        // 如果 fillNullOnError 为 true，则填充 null
        cellData = null;
      }

      record[field.id] = cellData;
    }

    return record;
  }
}

export class ExcelWorkbookWrapper {
  private readonly _workbook: Excel.Workbook;

  static async createFromFilePath(filePath: string): Promise<ExcelWorkbookWrapper> {
    const workbook = new Excel.Workbook();

    try {
      if (filePath.endsWith('.csv')) {
        await workbook.csv.readFile(filePath);
      } else if (filePath.endsWith('.xlsx')) {
        await workbook.xlsx.readFile(filePath);
      } else {
        throw new ServerError(errors.database.excel_unsupported_file_format);
      }
    } catch (__) {
      throw new ServerError(errors.database.excel_unsupported_file_format);
    }

    return ExcelWorkbookWrapper.create(workbook);
  }

  static create(workbook: Excel.Workbook): ExcelWorkbookWrapper {
    return new ExcelWorkbookWrapper(workbook);
  }

  private constructor(workbook: Excel.Workbook) {
    this._workbook = workbook;
  }

  public firstSheet(): Excel.Worksheet {
    const sheet = this._workbook.worksheets[0];
    if (!sheet) {
      throw new ServerError(errors.database.excel_empty_file);
    }
    return sheet;
  }

  public getFirstValidatedSheet(fields: DatabaseFieldWithId[], locale: LocaleType): ValidatedExcelSheet {
    const sheet = this.firstSheet();

    return ValidatedExcelSheet.createAndValidate(sheet, fields, locale);
  }

  public totalRows(): number {
    return this.firstSheet().rowCount - 1;
  }
}
