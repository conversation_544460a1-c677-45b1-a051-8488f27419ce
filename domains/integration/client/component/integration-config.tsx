import { useRouter } from 'next/navigation';
import type React from 'react';
import { useMemo, useState } from 'react';
import { useApiCaller } from '@bika/api-caller/context';
import { getIntegrationTypesConfig } from '@bika/contents/config/client/integration/integrations';
import { useLocale } from '@bika/contents/i18n';
import { useResourceEditorContext } from '@bika/types/editor/context';
import { type SpaceIntegration, type Integration, defaultIntegrationBO } from '@bika/types/integration/bo';
import type { IntegrationVO } from '@bika/types/integration/vo';
import { useSpaceContextForce } from '@bika/types/space/context';
import { Button } from '@bika/ui/button';
import { useSnackBar } from '@bika/ui/snackbar';
import { IntegrationBOInput } from '../../../editor/client/resource-editor/integration/integration-bo-input';
import { isServer } from '../../../shared/client/utils';

interface IIntegrationConfigProps {
  data?: IntegrationVO;
  type: SpaceIntegration['type'];
}

export const IntegrationConfig: React.FC<IIntegrationConfigProps> = ({ data, type }) => {
  const isUpdate = !!data;
  const [loading, setLoading] = useState(false);
  const { toast } = useSnackBar();
  const router = useRouter();
  const locale = useLocale();
  const editorContext = useResourceEditorContext();
  const { t } = locale;

  const [value, setValue] = useState<Integration>(() =>
    isUpdate ? data.bo : (defaultIntegrationBO(type, locale.lang) as Integration),
  );

  const integrationsConfigs = useMemo(() => getIntegrationTypesConfig(locale), [locale]);

  const { trpcQuery, trpc } = useApiCaller();
  const utils = trpcQuery.useUtils();

  const integrationUpdate = trpcQuery.integration.update.useMutation();
  const integrationCreate = trpcQuery.integration.create.useMutation();

  const spaceContext = useSpaceContextForce();

  const onClose = () => {
    router.back();
    spaceContext.showUIModal(null);
  };

  const createIntegration = async (integration: Integration) => {
    const { id: integrationId } = await integrationCreate.mutateAsync({
      spaceId: spaceContext.data.id,
      data: integration as SpaceIntegration,
    });
    utils.integration.list.invalidate();
    const { authLink } = await trpc.space.getIntegrationAuthorizationUrl.mutate({
      spaceId: spaceContext.data.id,
      integrationId,
    });
    if (authLink) {
      window.open(authLink, '_blank');
    }
    onClose();
  };

  const updateIntegration = async (integration: Integration) => {
    if (!data) {
      return;
    }
    await integrationUpdate.mutateAsync({
      spaceId: spaceContext.data.id,
      id: data.id,
      data: integration as SpaceIntegration,
    });
    onClose();
  };

  const isAdvertise = integrationsConfigs[type].type === 'ADVERTISE';

  const onConnect = () => {
    if (isServer) return;

    window.open(integrationsConfigs[type].advertisementConfig.redirectUrl, '_blank');
  };

  const onSubmit = () => {
    setLoading(true);
    if (isUpdate) {
      updateIntegration(value);
      return;
    }
    createIntegration(value);
  };

  return (
    <div className={'h-[544px]'}>
      <div className={'flex flex-col h-full'}>
        <div className={'space-y-4 flex-1'}>
          <IntegrationBOInput value={value} onChange={setValue} locale={locale} api={editorContext.api} />
        </div>
        <div className={'flex justify-center space-x-4'}>
          {isAdvertise ? (
            <Button color={'primary'} onClick={onConnect} loading={loading}>
              {t.action.more}
            </Button>
          ) : (
            <>
              <Button type="submit" color={'primary'} onClick={onSubmit} loading={loading}>
                {t.buttons.confirm}
              </Button>
              <Button color="neutral" variant="soft" onClick={onClose}>
                {t.buttons.cancel}
              </Button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};
