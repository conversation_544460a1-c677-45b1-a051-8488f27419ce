import React from 'react';
import type { ILocaleContext } from '@bika/contents/i18n/context';
import { HeaderTitleTabs } from '@bika/domains/node/client/header/header-title-tabs-component';
import type { WidgetRenderVO } from '@bika/types/dashboard/vo';
import DashboardOutlined from '@bika/ui/icons/components/dashboard_outlined';
import { Box, Divider, Grid } from '@bika/ui/layouts';
import { WidgetsVOArrayRenderer } from './widgets-vo-array-renderer';

interface Props {
  widgets: WidgetRenderVO[];
  locale: ILocaleContext;
  headerTitle?: string;
}

export function PureDashboardRenderer({ headerTitle, widgets, locale }: Props) {
  const { t } = locale;

  return (
    <>
      {headerTitle && (
        <>
          <HeaderTitleTabs startDecorator={<DashboardOutlined color={'var(--text-secondary)'} />} name={headerTitle} />
          <Divider />
        </>
      )}

      <Box sx={{ py: 3, overflowY: 'auto', height: 'calc(100vh - 48px - 61px)' }}>
        <Grid container spacing={2} height="100%">
          <WidgetsVOArrayRenderer value={widgets} locale={locale} />
        </Grid>
      </Box>
    </>
  );
}
