import { ILocaleContext } from '@bika/contents/i18n';
import { AiPageNodeBO, type AiPageSingleHTMLData } from '@bika/types/ai/bo';
import { INodeResourceApi } from '@bika/types/node/context';
import { useSpace<PERSON>ontext<PERSON><PERSON>ce, useSpaceRouter } from '@bika/types/space/context';
import { Button } from '@bika/ui/button-component';
import GenerateAiOutlined from '@bika/ui/icons/doc_hide_components/generate_ai_outlined';
import { VariablesCodeInput } from '@bika/ui/shared/types-form/variables-code-input';

interface IAIPageBOInputProps {
  locale: ILocaleContext;
  api: INodeResourceApi;
  required?: boolean;
  label?: string;
  setErrors?: (errors?: Record<string, string>) => void;
  value: AiPageNodeBO;
  setValue: (value: AiPageNodeBO) => void;
}

function AIPageSinglePageDataInput(props: {
  data: AiPageSingleHTMLData;
  setData: (newData: AiPageSingleHTMLData) => void;
  locale: ILocaleContext;
}) {
  const { t } = props.locale;
  return (
    <>
      <VariablesCodeInput
        language="html"
        label={t.resource.ai_page.settings_html_title}
        description={t.resource.ai_page.settings_html_description}
        placeholder={t.resource.ai_page.settings_html_placeholder}
        mode="code"
        locale={props.locale}
        value={props.data.content || ''}
        onChange={(v) => {
          props.setData({ ...props.data, content: v || undefined });
        }}
      />
    </>
  );
}
export const AIPageBOInput = (props: IAIPageBOInputProps) => {
  const { t } = props.locale;
  const spaceContext = useSpaceContextForce();
  const { useParams } = useSpaceRouter();
  const { nodeId } = useParams<{ nodeId: string }>();

  return (
    <div>
      {(!props.value.data || props.value.data?.kind === 'SINGLE_HTML') && (
        <AIPageSinglePageDataInput
          data={
            props.value.data || {
              kind: 'SINGLE_HTML',
              content: '',
            }
          }
          setData={(newData) => {
            props.setValue({
              ...props.value,
              data: newData,
            });
          }}
          locale={props.locale}
        />
      )}
      {props.value.data?.kind === 'SLIDES' && <>TODO Slides</>}
      {/* 等 Copilot 功能做完再打开 */}
      <Button
        variant="soft"
        color="neutral"
        sx={{ gap: 0.5, mt: '8px' }}
        onClick={() => {
          spaceContext.showAICopilot({
            type: 'node',
            nodeId: nodeId!,
          });
          const url = new URL(window.location.href);
          url.searchParams.delete('drawer');
          url.searchParams.set('ModifyHTML', '1');
          window.history.pushState({}, '', url.toString());
        }}
      >
        <GenerateAiOutlined />
        {t.resource.ai_page.settings_html_copilot}
      </Button>
    </div>
  );
};
