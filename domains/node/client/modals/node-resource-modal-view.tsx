import { usePathname } from 'next/navigation';
import React, { useEffect, useMemo, useState } from 'react';
import { useApiCaller } from '@bika/api-caller';
import { useLocale } from '@bika/contents/i18n/context';
import type { NodeResourceScope } from '@bika/types/node/bo';
import { useNodeResourceApiContext } from '@bika/types/node/context';
import type { FolderVO, NodeTreeVO } from '@bika/types/node/vo';
import type { INodeSelectValue } from '@bika/types/space/bo';
import { useSpaceContextForce, useSpaceRouter } from '@bika/types/space/context';
import { Breadcrumbs } from '@bika/ui/breadcrumbs';
import { Button } from '@bika/ui/button';
import CheckOutlined from '@bika/ui/icons/components/check_outlined';
import ChevronRightOutlined from '@bika/ui/icons/components/chevron_right_outlined';
import { Box, Stack } from '@bika/ui/layouts';
import { List, ListItem, ListItemButton } from '@bika/ui/list';
import { NodeIcon } from '@bika/ui/node/icon';
import { SearchInputComponent } from '@bika/ui/search-input';
import { Skeleton } from '@bika/ui/skeleton';
import { useSnackBar } from '@bika/ui/snackbar';
import { Typography } from '@bika/ui/texts';
import { type AIChatSelector, useAIChatSession } from '../../../ai/client/chat/hooks/use-ai-chat-cache';
import { useSpaceModalContext } from '../../../space/client/modals';
import { useNodeTool } from '../../../space/client/sidebar/hooks/use-node-tool';
import { getExpandNode } from '../../../space/client/sidebar/utils/match-template-util';

interface Props {
  value: INodeSelectValue;
  onClose: () => void;
}

interface Path {
  id: string | undefined;
  name: string;
}

export function NodeResourceModalView(props: Props) {
  const { value, onClose } = props;

  const { trpcQuery } = useApiCaller();
  const { i, t } = useLocale();
  const spaceContext = useSpaceContextForce();
  const api = useNodeResourceApiContext();
  const modalCtx = useSpaceModalContext();
  const { toast } = useSnackBar();
  const { useParams } = useSpaceRouter();
  const paramsNodeId = useParams<{ nodeId: string }>().nodeId;
  const pathname = usePathname();

  const { useRootNode, data: space } = spaceContext;
  const spaceId = space.id;
  const { rootNode, setRootNode, refetch: refetchRootNode } = useRootNode();
  const { rootNode: privateRootNode, refetch: refetchPrivateRootNode } = useRootNode('PRIVATE');

  const { handleReorderItem } = useNodeTool({
    spaceId,
    setNode: setRootNode,
    node: rootNode,
  });

  const mutate = api.node.useNodeResourceBOMutation();

  const spaceName = space.name;
  const nodeScope = value.kind === 'folder' ? value.node.scope : 'SPACE';

  const DEFAULT_PATH: Path[] = useMemo(() => [{ id: rootNode.id, name: rootNode.name }], [rootNode]);

  const cacheSelector: AIChatSelector = useMemo(() => {
    if (value.kind === 'resource') {
      if (paramsNodeId?.startsWith('ain')) {
        return { type: 'agent', spaceId, agent: { type: 'node', nodeId: paramsNodeId } };
      }
      if (pathname.endsWith('ai-app-builder')) {
        return { type: 'agent', spaceId, agent: { type: 'expert', expertKey: 'builder' } };
      }
      if (pathname.endsWith('supervisor')) {
        return { type: 'agent', spaceId, agent: { type: 'expert', expertKey: 'supervisor' } };
      }
      if (paramsNodeId && !paramsNodeId.startsWith('ain')) {
        return { type: 'copilot', spaceId, copilot: { type: 'node', nodeId: paramsNodeId } };
      }
      return {
        type: 'space',
        spaceId,
      };
    }
    return {
      type: 'space',
      spaceId,
    };
  }, [value, paramsNodeId, spaceId, pathname]);

  const { session } = useAIChatSession(cacheSelector);

  const [selectedResources, setSelectedResources] = useState<NodeTreeVO[]>([]);

  useEffect(() => {
    if (session.contexts) {
      setSelectedResources(
        session.contexts.filter((context) => context.type === 'node').map((context) => context.node),
      );
    }
  }, [session.contexts]);

  const [toScope, setToScope] = useState<NodeResourceScope | null>(null);
  const [paths, setPaths] = useState<Path[]>(DEFAULT_PATH);
  const currentRootNode = toScope === 'PRIVATE' ? privateRootNode : rootNode;
  const [cacheNodes, setCacheNodes] = useState<NodeTreeVO>(currentRootNode);
  const [selectedNodeId, setSelectedNodeId] = useState<string>(
    toScope === 'PRIVATE' ? privateRootNode.id : rootNode.id,
  );
  const [searchQuery, setSearchQuery] = useState<string>('');

  const { data: result } = trpcQuery.node.list.useQuery({
    spaceId,
    name: searchQuery,
    parentId: toScope === 'PRIVATE' ? privateRootNode.id : rootNode.id,
  });

  const { data: nodeDetailData, isLoading } = trpcQuery.node.detail.useQuery({ id: selectedNodeId });

  const isChoosePrivateNodeDestination = toScope === null && nodeScope === 'PRIVATE' && searchQuery === '';

  useEffect(() => {
    if (nodeDetailData) {
      setCacheNodes(nodeDetailData.resource as FolderVO);

      if (selectedNodeId === rootNode.id) {
        setPaths(DEFAULT_PATH);
      } else {
        setPaths((prev) => {
          const index = prev.findIndex((path) => path.id === selectedNodeId);
          if (index === -1) {
            return [...prev, { id: selectedNodeId, name: i(nodeDetailData.name) }];
          }
          return prev.slice(0, index + 1);
        });
      }
    }
  }, [i, nodeDetailData, selectedNodeId, rootNode.id]);

  useEffect(() => {
    if (nodeDetailData && value.kind === 'folder') {
      const Title = (
        <Typography width="100%" height="56px" flexGrow={1} textColor={'var(--text-primary)'} level="h6">
          {t('resource.move_resource_to', {
            name: i(value.node.name),
            destination: i(nodeDetailData?.name) === 'ROOT' ? spaceName : i(nodeDetailData?.name),
          })}
        </Typography>
      );
      modalCtx.setTitle(Title);
    }
    if (value.kind === 'resource') {
      const Title = (
        <Typography width="100%" height="56px" flexGrow={1} textColor={'var(--text-primary)'} level="h6">
          {'选择节点作为数据源'}
        </Typography>
      );
      modalCtx.setTitle(Title);
    }
  }, [nodeDetailData]);

  if (isLoading || !nodeDetailData) return <Skeleton pos="NODE_INFO" />;

  const onNodeTreeClick = async (id: string) => {
    setSearchQuery('');

    if (id === rootNode.id) {
      setToScope(null);
      setSelectedNodeId(rootNode.id);
      setPaths(DEFAULT_PATH);
      setCacheNodes(rootNode);
      return;
    }

    setSelectedNodeId(id);
  };

  const handleSubmit = async () => {
    if (value.kind === 'resource' && selectedResources.length > 0) {
      session.setContexts((_pre) => {
        const pre = _pre?.filter((item) => item.type !== 'node') ?? [];
        const newContexts = selectedResources.map((node) => ({
          type: 'node' as const,
          node,
        }));
        return [...pre, ...newContexts];
      });
      spaceContext?.showUIModal(null);
    }
    if (!selectedNodeId) return;
    if (value.kind === 'folder') {
      const node = value.node;
      try {
        const oldNodeParent = node.parentId ? getExpandNode(currentRootNode, node.parentId) : currentRootNode;
        const nodeIndex = oldNodeParent?.children?.findIndex((item) => item.id === node.id) || 0;
        const nodeParentId = oldNodeParent?.id || null;
        // private node move
        if (node.scope === 'PRIVATE') {
          await mutate.move({
            id: node.id,
            data: {
              preNodeId: null,
              parentId: selectedNodeId,
              scope: toScope ?? nodeScope,
            },
          });
          await refetchRootNode();
          await refetchPrivateRootNode();
        } else {
          handleReorderItem(currentRootNode, {
            itemId: node.id,
            oldPosition: { parentId: nodeParentId, index: nodeIndex },
            // 默认移动到第一个位置
            newPosition: { parentId: selectedNodeId, index: 0 },
          });
        }

        toast(t.resource.move_resource_success, { variant: 'success' });

        spaceContext?.showUIModal(null);
      } catch (error) {
        if (error instanceof Error) {
          toast(error.message, { variant: 'error' });
        } else {
          toast(t.resource.move_resource_error, { variant: 'error' });
          console.error(error);
        }
      }
    }
  };

  const isResourceFolder = (item: NodeTreeVO) => ['ROOT', 'FOLDER', 'TEMPLATE'].includes(item.type);
  const isNotAIResource = (item: NodeTreeVO) => !['PAGE', 'AI'].includes(item.type);

  const renderList = (items: NodeTreeVO[]) => {
    const list = value.kind === 'folder' ? items.filter(isResourceFolder) : items.filter(isNotAIResource);

    if (list.length === 0) {
      return (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            width: '100%',
          }}
        >
          <Typography level="body-md">{t.resource.folder_no_content}</Typography>
        </Box>
      );
    }

    return (
      <List>
        {list.map((item) => (
          <ListItem key={item.id}>
            <ListItemButton
              sx={{
                justifyContent: 'space-between',
                alignItems: 'center',
                height: '40px',
                '&:hover': {
                  backgroundColor: 'var(--hover) !important',
                },
                '&:active': {
                  backgroundColor: 'var(--active) !important',
                },
                '&:focus': {
                  backgroundColor: 'var(--active) !important',
                },
              }}
              onClick={() => {
                if (isResourceFolder(item)) {
                  onNodeTreeClick(item.id);
                } else {
                  setSelectedResources((prev) => {
                    const isSelected = prev.some((res) => res.id === item.id);
                    if (isSelected) {
                      return prev.filter((res) => res.id !== item.id);
                    }
                    const newItem = {
                      ...item,
                      path:
                        item.path || paths.length > 1
                          ? `/${paths
                              .slice(1)
                              .map((path) => path.name)
                              .join('/')}`
                          : undefined,
                    };
                    return [...prev, newItem];
                  });
                }
              }}
            >
              <Stack direction="row" alignItems="center" spacing={2} sx={{ overflow: 'hidden' }}>
                <NodeIcon
                  value={
                    item.icon
                      ? {
                          kind: 'avatar',
                          avatar: item.icon,
                          name: item.name,
                        }
                      : { kind: 'node-resource', nodeType: item.type }
                  }
                  size={24}
                />
                <Stack direction="column">
                  <Typography level="body-md">{item.name}</Typography>
                  {searchQuery && (
                    <Typography level="body-sm" sx={{ color: 'var(--text-secondary)' }}>
                      {paths.map((path) => (path.name === 'ROOT' ? spaceName : path.name)).join('/')}
                    </Typography>
                  )}
                </Stack>
              </Stack>
              {isResourceFolder(item) && (
                <Box sx={{ width: 24, flexShrink: 0 }}>
                  <ChevronRightOutlined />
                </Box>
              )}
              {selectedResources.some((res) => res.id === item.id) && (
                <Box sx={{ width: 24, flexShrink: 0 }}>
                  <CheckOutlined color="var(--brand)" />
                </Box>
              )}
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    );
  };

  return (
    <Stack direction="column" justifyContent="space-between" height="560px">
      <Stack
        direction="column"
        justifyContent="space-between"
        sx={{
          flex: '1',
          '& .MuiTypography-b3': {
            height: 24,
          },
          height: '500px',
        }}
      >
        <Box className={'flex-none'}>
          <SearchInputComponent value={searchQuery} onChange={(value) => setSearchQuery(value)} />
        </Box>
        <Stack direction="column" height="460px" sx={{ flex: 1 }}>
          <Breadcrumbs>
            {paths.map((path, index) => (
              <Typography
                key={path.id}
                level="body-md"
                onClick={() => {
                  if (index === paths.length - 1 || !path.id) return;
                  onNodeTreeClick(path.id);
                }}
                sx={{
                  cursor: 'pointer',
                  color: index === paths.length - 1 ? 'var(--brand)' : 'var(--text-secondary)',
                  fontWeight: index === paths.length - 1 ? 'bold' : 'normal',
                }}
              >
                {path.name === 'ROOT' ? spaceName : path.name}
              </Typography>
            ))}
          </Breadcrumbs>
          {isChoosePrivateNodeDestination ? (
            <List>
              <ListItem>
                <ListItemButton
                  sx={{
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    height: '40px',
                  }}
                  onClick={() => {
                    setToScope('SPACE');
                    setPaths((p) => [
                      ...p,
                      {
                        id: rootNode.id,
                        name: t.navbar.team_resources,
                      },
                    ]);
                    setCacheNodes(rootNode);
                  }}
                >
                  <Typography level="body-md">{t.navbar.team_resources}</Typography>
                </ListItemButton>
              </ListItem>
              <ListItem>
                <ListItemButton
                  sx={{
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    height: '40px',
                  }}
                  onClick={() => {
                    setToScope('PRIVATE');
                    setPaths((p) => [
                      ...p,
                      {
                        id: privateRootNode.id,
                        name: t.navbar.private,
                      },
                    ]);
                    setCacheNodes(privateRootNode);
                    setSelectedNodeId(privateRootNode.id);
                  }}
                >
                  <Typography level="body-md">{t.navbar.private}</Typography>
                </ListItemButton>
              </ListItem>
            </List>
          ) : (
            <Stack spacing={1} direction="column" sx={{ flex: 1, overflowY: 'auto' }}>
              {renderList((searchQuery ? result : cacheNodes.children) ?? [])}
            </Stack>
          )}
        </Stack>
      </Stack>
      <Stack pt={2} direction="row" spacing={2} alignItems="center" justifyContent="center" height="60px">
        <Button
          type="button"
          variant="solid"
          color="primary"
          size="lg"
          onClick={handleSubmit}
          disabled={isChoosePrivateNodeDestination}
        >
          {value.kind === 'folder' ? t.action.move : t.action.ok}
        </Button>
        <Button size="lg" variant="outlined" color="neutral" onClick={onClose}>
          {t.action.cancel}
        </Button>
      </Stack>
    </Stack>
  );
}
