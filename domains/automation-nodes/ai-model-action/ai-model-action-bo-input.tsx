import React from 'react';
import { type AIModelAction } from '@bika/types/automation/bo';
import type { ResourceFormBase } from '@bika/ui/shared/types-form/types';
import { VariablesTextInput } from '@bika/ui/shared/types-form/variables-text-input';
import { AIModelsSelector } from '../../ai/client/ai-models/ai-models-selector/ai-models-selector';

interface Props extends ResourceFormBase {
  value: AIModelAction;
  onChange: (value: AIModelAction) => void;
}

export function AIModelActionBOInput(props: Props) {
  const { api, locale, parentActionId } = props;
  const { t } = locale;

  const variables = api.automation.getAutomationGlobalVariables(props.value.id || parentActionId);

  return (
    <>
      <AIModelsSelector
        value={props.value.input.model}
        onChange={(model) => props.onChange({ ...props.value, input: { ...props.value.input, model } })}
      />

      <VariablesTextInput
        label={t.automation.action.openai_generate_text.prompt}
        automationVariables={variables}
        value={props.value.input.prompt}
        locale={locale}
        onChange={(prompt) => props.onChange({ ...props.value, input: { ...props.value.input, prompt } })}
      />
    </>
  );
}
