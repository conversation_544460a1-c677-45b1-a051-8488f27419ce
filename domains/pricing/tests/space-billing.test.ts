import { expect, test, beforeAll } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';

/**
 * 模拟一个员工，经历Scrum工作流程
 */
// Login and Register user

beforeAll(async () => {});

const usdPlusYearly = 999;
const usdPlusMonthly = 1199;

test('Space Daily / Monthly AI Credit', async () => {
  const {
    user: userSO,
    space: spaceSO,
    member: _memberSO,
    rootFolder: _rootFolder,
  } = await MockContext.initUserContext();

  const spaceBill = spaceSO.billing;
  const spaceCoins = await spaceBill.getCoinsAccount();

  // 新空间站(免费的)哦，有 1 个席位，那么 每月 0 和每日 3000 （Virtual 一共 3000）
  expect(await spaceCoins.virtualCredit()).toBe(BigInt(0 + 3000));

  // 假设：新空间站，个人加入 有自己的 1000 奖励
  await spaceCoins.earn(BigInt(1000), 'CREDIT', { reason: 'gm', description: 'Test Credit Coin' });
  expect(await spaceCoins.virtualBalance()).toBe(BigInt(0 + 3000 + 1000));

  // 假设开始消耗了，调用 AI 消耗 100 virtual credit (优先使用每天 virtual 的)
  await spaceCoins.redeem(BigInt(100), { reason: 'gm', description: 'Test Credit Coin' });

  expect(await spaceCoins.virtualCredit()).toBe(BigInt(0 + 3000 - 100));
  expect(await spaceCoins.virtualBalance()).toBe(BigInt(0 + 3000 + 1000 - 100));

  // 假设消耗3000,由于 virtual 只有 2900，不够，要从 credit 里扣多 100！
  expect(await spaceCoins.virtualCredit()).toBe(BigInt(2900));
  await spaceCoins.redeem(BigInt(3000), { reason: 'gm', description: 'Test Credit Coin' });
  expect(await spaceCoins.virtualCredit()).toBe(BigInt(0 + 3000 - 100 - 2900));
  expect(await spaceCoins.credit).toBe(BigInt(900));
  expect(await spaceCoins.virtualBalance()).toBe(BigInt(0 + 3000 + 1000 - 100 - 3000));
});

// 验证计价准确性
test.skip('Billing Plan Test Full', async () => {
  //   // 这是一个客户在绑卡的前提下进行的订阅支付测试, 先禁用掉,以后再处理它
  //   const {
  //     user: userSO,
  //     space: spaceSO,
  //     member: _memberSO,
  //     rootFolder: _rootFolder,
  //   } = await MockContext.initUserContext();
  //   await userSO.updateSettings({
  //     locale: 'en',
  //   });
  //   const userId = userSO.id;
  //   // 默认是免费空间站
  //   const spaceBilling = spaceSO.billing;
  //   const currentSubscription = await spaceBilling.getCurrentSubscription();
  //   expect(currentSubscription).toBeNull();
  //   // 测试stripe price获取是否成功
  //   // const stripePrice = await db.stripe.getStripePriceConfig('PLUS', 'month');
  //   // expect(stripePrice).not.toBeNull();
  //   // ========== 以下是华丽的价格演算 =================
  //   // 假设2个member要升级PLUS，计算要多少钱
  //   // 先加多个member
  //   const rootTeam = await spaceSO.getRootTeam();
  //   const secondSeat = await MockContext.createUser('second user');
  //   await spaceSO.joinUser(secondSeat.id, rootTeam.id);
  //   // 计算多少钱？（价格页面也要显示）
  //   const priceWithoutBKC = await spaceBilling.calcUpgradePlanPrice(userId, 'PLUS', 'month', 'USD');
  //   const { checkoutPrice: priceWithoutBKCP } = priceWithoutBKC;
  //   expect(priceWithoutBKCP.originalAmount).toBe(usdPlusMonthly * 2); // $1.29 * 2 / 12.99 * 2
  //   expect(priceWithoutBKCP.amount).toBe(usdPlusMonthly * 2); // $12.99 * 2
  //   expect(priceWithoutBKCP.currencyCode).toBe('USD'); // 英语返回USD
  //   // 如果算上BKC呢？  开发环境新注册用户默认有100BKC，抵扣1元 ，月付是$1.29一个月，2个席位是$2.58,减100BKC，US$1.58
  //   const priceWithBKC = await spaceBilling.calcUpgradePlanPrice(userId, 'PLUS', 'month', 'USD', 'BKC_ALL');
  //   const { checkoutPrice: priceWithBKCP } = priceWithBKC;
  //   expect(priceWithBKCP.originalAmount).toBe(usdPlusMonthly * 2); // 原价
  //   expect(priceWithBKCP.amount).toBe(usdPlusMonthly * 2 - 100); // -100BKC
  //   expect(priceWithBKCP.byCoins).toBe(100); // -100BKC
  //   // 年付
  //   const priceWithBKCYearly = await spaceBilling.calcUpgradePlanPrice(userId, 'PLUS', 'year', 'USD', 'BKC_ALL');
  //   const { checkoutPrice: priceWithBKCYearlyP } = priceWithBKCYearly;
  //   expect(priceWithBKCYearlyP.originalAmount).toBe(usdPlusYearly * 2 * 12); // 原价
  //   expect(priceWithBKCYearlyP.amount).toBe(usdPlusYearly * 2 * 12 - 100); // -100BKC
  //   expect(priceWithBKCYearlyP.byCoins).toBe(100); // -100BKC
  //   // 香港plus月付是HK$10，2个席位是HK$20，即2000，减100BKC，抵扣(HK$10 / US$1.29) * 100 = 775，得2000 - 775 = 1225
  //   const hkdPriceWithBKC = await spaceBilling.calcUpgradePlanPrice(userId, 'PLUS', 'month', 'HKD', 'BKC_ALL');
  //   const { checkoutPrice: hkdPriceWithBKCP } = hkdPriceWithBKC;
  //   expect(hkdPriceWithBKCP.originalAmount).toBe(9400 * 2); // 原价, HK$94 * 2 = 188
  //   expect(hkdPriceWithBKCP.amount).toBe(18016); // 10200 * 2 - Math.round((1000 / 129) * 100)); // 港币$12.25
  //   expect(hkdPriceWithBKCP.byCoins).toBe(100); // 消耗100BKC，刚好全部用完
  //   // 发多100BKC吧！
  //   const bkcAccount = await userSO.coins.getAccount();
  //   expect(bkcAccount.balance).toBe(BigInt(100)); // 这是内存校验是不是200，下面是数据库里重新校验
  //   await bkcAccount.addTransaction(BigInt(100), 'CREDIT', 'EARN', {
  //     reason: 'gm',
  //     description: 'Billing Test Credit Coin',
  //   });
  //   expect(bkcAccount.balance).toBe(BigInt(200)); // 这是内存校验是不是200，下面是数据库里重新校验
  //   const bkcAccount2 = await userSO.coins.getAccount();
  //   expect(bkcAccount2.balance).toBe(BigInt(200)); // 数据库里重新校验
  //   // 多了100BKC，一共200BKC，在看看折扣多少钱！
  //   // 香港plus月付是HK$10，2个席位是HK$20，即2000，减200BKC，抵扣(HK$10 / US$1.29) * 200 = 1550，得2000 - 1550 = 450
  //   const hkdPriceWithBKC2 = await spaceBilling.calcUpgradePlanPrice(userId, 'PLUS', 'month', 'HKD', 'BKC_ALL');
  //   const { checkoutPrice } = hkdPriceWithBKC2;
  //   expect(checkoutPrice.originalAmount).toBe(9400 * 2); // 原价, HK$94 * 2 = 188
  //   expect(checkoutPrice.amount).toBe(17232); // 450);
  //   expect(checkoutPrice.byCoins).toBe(200); // BKC全部消耗完了
  //   // 这次发100000BKC，随便花了！10w (注意，这里我充值了Currency类型的Coin)
  //   await bkcAccount.addTransaction(BigInt(100000), 'CREDIT', 'EARN', {
  //     reason: 'gm',
  //     description: 'Billing Test Credit Coin, 100000BKC',
  //   });
  //   expect(bkcAccount.balance).toBe(BigInt(100200));
  //   // 花不完的钱了，再买
  //   // 月付是$1.29一个月，2个席位是$2.58,减258BKC，
  //   const priceWithBKC4 = await spaceBilling.calcUpgradePlanPrice(userId, 'PLUS', 'month', 'USD', 'BKC_ALL');
  //   const { checkoutPrice: priceWithBKC4P } = priceWithBKC4;
  //   expect(priceWithBKC4P.originalAmount).toBe(usdPlusMonthly * 2);
  //   expect(priceWithBKC4P.amount).toBe(0);
  //   expect(priceWithBKC4P.byCoins).toBe(usdPlusMonthly * 2);
  //   // ========== 以上是华丽的价格演算 =================
  //   // 假设，直接Bika Credit购买，不走Stripe
  //   // 在无需money，只需Bika Coin就可以完全成交的情况
  //   // 创建Payment，并立刻有了sbuscription
  //   const upgradeProcess = await spaceBilling.upgradePlanWithBikaCoin(userId, 'PLUS', 'month', 'USD');
  //   const { subscription, payment, stripeCheckoutSession } = upgradeProcess;
  //   expect(stripeCheckoutSession).toBeNull(); // money < 0，无需Stripe支付，所以没有session
  //   expect(payment.checkoutPrice.byCoins).toBe(usdPlusMonthly * 2); // 258 BKC
  //   // 应该扣掉bika coin，判断扣了没
  //   const bkcAccount3 = await userSO.coins.getAccount();
  //   expect(bkcAccount3.balance).toBe(BigInt(100200 - usdPlusMonthly * 2)); // 花掉了258BKC，剩余99942
  //   // subscription 可以订阅不同的商品，取决于SKU type，也有可能是Addon
  //   const skuId = subscription.model.skuId;
  //   expect(subscription.model.skuType).toBe('SPACE_PLAN');
  //   const spacePlanType = subscription.toVO(); // getPlanSkuIdFromPlanId(skuId);
  //   expect(spacePlanType.plan).toBe('PLUS');
  //   expect(payment.checkoutPrice.sku.id).toBe(skuId);
  //   expect(subscription.model.recurring).toBe('month');
  //   // 支付验证
  //   expect(payment.checkoutPrice.amount).toBe(0);
  //   expect(payment.checkoutPrice.byCoins).toBe(usdPlusMonthly * 2); // 258 BKC
  //   expect(payment.checkoutPrice.currencyCode).toBe('USD');
  //   // ================== 以下是真钱支付 ==================
  //   // 假设，Stripe支付完成，虚构一个payment(checkout session)，回调支付接口。
  //   // 这个用于真付费，并要进行payment验证
  //   // 通常需要校验Stripe Checkout Session Callback，检查Payment是否成功，这里假设校验完了，创建payment
  //   // 由于之前用bika coin已经升级了，如果再想升级相同plan、interval，会抛异常
  //   await expect(async () =>
  //     spaceBilling.generateUpgradePlanStripeCheckoutSession(userId, 'PLUS', 'month', 'USD', 'MONEY_ONLY'),
  //   ).rejects.toThrowError(/same/);
  //   // 从plus月付，升级到PLUS年付，计算补偿proration，BKC
  //   const { checkoutPrice: bkcUpgradePrice } = await spaceBilling.calcUpgradePlanPrice(
  //     userId,
  //     'PLUS',
  //     'year',
  //     'USD',
  //     'BKC_ALL',
  //   );
  //   expect(bkcUpgradePrice.originalAmount).toBe(usdPlusYearly * 2 * 12); // 原价
  //   expect(bkcUpgradePrice.amount).toBe(0); // 原价,上一次没有用现金，这次就没法proration补偿了，纯BKC支付也不需要money
  //   expect(bkcUpgradePrice.prorationAmount).toBe(0); // 没有proration
  //   expect(bkcUpgradePrice.prorationCoinsCredit).toBe(usdPlusMonthly * 2); // 补偿金币(上一轮用的是coins credit)
  //   expect(bkcUpgradePrice.prorationCoinsCurrency).toBe(0); // 补偿金币(上一轮没有用coins currency)
  //   expect(bkcUpgradePrice.prorationCoins).toBe(usdPlusMonthly * 2); // 补偿金币
  //   expect(bkcUpgradePrice.byCoins).toBe(usdPlusYearly * 12 * 2 - usdPlusMonthly * 2); // 99 * 12 * 2BKC - $1.29 * 2，上一次支付过了，这次减少
  //   // 升级Subscription, plus monthly -> plus yearly
  //   // 从plus月付，升级到PLUS年付，用现金
  //   const customer = await spaceBilling.getOrCreateCustomer(userId);
  //   // 加测试卡
  //   await db.stripe.addTestCardToCustomer(customer.model.platformCustomerId);
  //   const genSession = await spaceBilling.generateUpgradePlanStripeCheckoutSession(
  //     userId,
  //     'PLUS',
  //     'year',
  //     'USD',
  //     'MONEY_ONLY',
  //   );
  //   const {
  //     checkoutPrice: checkoutPriceMoney,
  //     payment: moneyPayment,
  //     stripeCheckoutSession: moneySession,
  //     skuConfig: moneySku,
  //   } = genSession;
  //   expect(moneySession).not.toBeNull(); // 有stripe session
  //   expect((moneyPayment.model.metadata! as any).stripeSessionId).toBe(moneySession!.id);
  //   // 测试，通过stripe session id，找到payment
  //   // const paymentByStripeSession = await PaymentSO.findOneByMetadata(['stripeSessionId'], moneySession!.id);
  //   // expect(paymentByStripeSession!.model.id).toBe(moneyPayment.model.id); // 一样的db po
  //   expect(checkoutPriceMoney.originalAmount).toBe(usdPlusYearly * 2 * 12); // 原价
  //   expect(checkoutPriceMoney.amount).toBe(usdPlusYearly * 2 * 12); // 原价，因为上一次，并没有用现金，这次就没法proration补偿了
  //   expect(moneyPayment.status).toBe('PENDING');
  //   // Stripe Webhook 秘钥
  //   const stripeWebhookSecret = process.env.STRIPE_WEBHOOK_SECRET as string;
  //   // 模拟 Stripe！
  //   // 模拟Stripe Webhook回调: checkou.session.completed
  //   const mockCheckoutSessionSuccessJSON = mockCheckoutSessionCompleted(moneySession!.id);
  //   const mockCheckoutSessionSuccessPayloadStr = JSON.stringify(mockCheckoutSessionSuccessJSON, null, 2);
  //   const stripeWebhookHeader1 = db.stripe.stripe.webhooks.generateTestHeaderString({
  //     payload: mockCheckoutSessionSuccessPayloadStr,
  //     secret: stripeWebhookSecret,
  //   });
  //   // 测试stripe event解析
  //   const stripeEvent = db.stripe.stripe.webhooks.constructEvent(
  //     mockCheckoutSessionSuccessPayloadStr,
  //     stripeWebhookHeader1,
  //     stripeWebhookSecret,
  //   );
  //   expect(stripeEvent.id).to.equal(mockCheckoutSessionSuccessJSON.id);
  //   // 继续，发webhook 模拟stripe callback
  //   const webhookRes = await stripeAPI.request('/webhooks', {
  //     body: mockCheckoutSessionSuccessPayloadStr,
  //     method: 'POST',
  //     headers: {
  //       'stripe-signature': stripeWebhookHeader1,
  //     },
  //   });
  //   expect(webhookRes.status).toBe(200); // 成功，session变成“过期了”
  //   const moneyPaymentAfterSuccess = await PaymentSO.init(moneyPayment.model.id);
  //   expect(moneyPaymentAfterSuccess!.status).toBe('SUCCESS');
  //   // 顾客
  //   const customerSO = await spaceBilling.getOrCreateCustomer(userId);
  //   const subs = await customerSO.getSubscriptions();
  //   expect(subs.length).toBe(1); // 1个订阅，上面两个都是升级PLAN，会互相覆盖，所以只有一个
  //   const sub1 = subs[0];
  //   const skuPlan = getSpacePlanTypeFromSkuId(sub1.model.skuId);
  //   const skuPlanConfig = getPlanConfigFromSkuId(sub1.model.skuId);
  //   expect(sub1.model.skuId).toBe(moneySku.id); // 升级成功
  //   // const stripeSub = await sub1.getStripeSubscription();
  //   // expect(stripeSub).toBeDefined();
  //   // 确定是变年付了
  //   expect(skuPlan).toBe('PLUS');
  //   expect(skuPlanConfig.interval).toBe('year');
  //   // const subs = await SubscriptionSO.getPlanBySpaceId(spaceId);
  //   // const sub2PlanVO = subscription2.toVO();
  //   // expect(sub2PlanVO.interval).toBe('year');
  //   // expect(sub2PlanVO.plan).toBe('PLUS');
  //   // Site Admin，能控制全站
  //   // const newAdmin = await AdminSO.addAdmin(userId);
  //   // expect(newAdmin.model.id).toBe(userId);
  //   // expect(await AdminSO.isAdmin(userId)).toBe(true);
  //   // 模拟Stripe Webhook回调: checkou.session.expired (超时没支付的情况)
  //   const mockExpired = await spaceBilling.generateUpgradePlanStripeCheckoutSession(
  //     userId,
  //     'PRO',
  //     'year',
  //     'USD',
  //     'MONEY_ONLY',
  //   );
  //   const { stripeCheckoutSession: mockSession } = mockExpired;
  //   const mockCheckoutSessionExpiredJSON = mockCheckoutSessionExpired(mockSession!.id);
  //   const mockCheckoutSessionExpiredPayloadStr = JSON.stringify(mockCheckoutSessionExpiredJSON, null, 2);
  //   const stripeWebhookHeader2 = db.stripe.stripe.webhooks.generateTestHeaderString({
  //     payload: mockCheckoutSessionExpiredPayloadStr,
  //     secret: stripeWebhookSecret,
  //   });
  //   const webhookResMock = await stripeAPI.request('/webhooks', {
  //     body: mockCheckoutSessionExpiredPayloadStr,
  //     method: 'POST',
  //     headers: {
  //       'stripe-signature': stripeWebhookHeader2,
  //     },
  //   });
  //   expect(webhookResMock.status).toBe(200); // 成功，payment会变“过期了”
  //   // const mockExpiredPayment = await PaymentSO.findOneByMetadata(['stripeSessionId'], mockSession!.id);
  //   // expect(mockExpiredPayment!.status).toBe('EXPIRED');
  //   // 先确定买什么产品吧？ Plus版
  //   // const prdSO = await ProductSO.getAdminProduct('SPACE_PLAN_PLUS');
  //   // 来，给它升级一下！支付方式：ADMIN霸道式
  //   // 创建一个霸道支付
  //   // const paymentSO = await PaymentSO.createAdminPayment(userId, prdSO);
  //   // // 支付里会自动创建admin顾客
  //   // const customerSOAfterPayment = await CustomerSO.init(paymentSO.model.customerId);
  //   // // 顾客ID匹配管理员 User ID
  //   // expect(customerSOAfterPayment.model.userId).toBe(customerSO.model.userId);
  //   // expect(customerSOAfterPayment.model.userId).toBe(userId);
  //   // // 有了支付信息，那么创建订阅吧！ADMIN支付无需检查真实金额，其它都要了
  //   // const subscriptionSO = await SubscriptionSO.create(spaceSO.id, paymentSO);
  //   // expect(subscriptionSO.model.productId).toBe(prdSO.model.id);
  //   // expect(subscriptionSO.model.product.skuId).toBe('SPACE_PLAN_PLUS');
  //   // // 嗯，升级一个space plan，并不是升级space的数据库这么简单，而是创建一个subscription订阅计划
  //   // // 订阅成功~ 来测试下
  //   // const billingType2 = await spaceBilling.getPlan();
  //   // expect(billingType2).toBe('PLUS');
  // }, 20000);
  // 上面那个是细节测试版，下面这个经过封装...
  // test('Billing Test Simple', async () => {
  //   const {
  //     user: userSO, space: spaceSO,
  //   } = await MockContext.initUserContext();
  //   const userId = userSO.id;
  //   const spaceBilling = spaceSO.billing;
  //   const newAdmin = await AdminSO.addAdmin(userId);
  //   expect(newAdmin.model.id).toBe(userId);
  //   expect(await AdminSO.isAdmin(userId)).toBe(true);
  //   // 设置成plus
  //   const subscription = await spaceBilling.setPlanByAdmin('PLUS', userId);
  //   expect(subscription.model.product.skuId).toBe('SPACE_PLAN_PLUS');
  //   const billingType = await spaceBilling.getPlan();
  //   expect(billingType).toBe('PLUS');
  //   // 升级成pro
  //   const subscription2 = await spaceBilling.setPlanByAdmin('PRO', userId);
  //   expect(subscription2.model.product.skuId).toBe('SPACE_PLAN_PRO');
  //   const billingType2 = await spaceBilling.getPlan();
  //   expect(billingType2).toBe('PRO');
  //   // 降级成plus
  //   const subscription3 = await spaceBilling.setPlanByAdmin('PLUS', userId);
  //   expect(subscription3.model.product.skuId).toBe('SPACE_PLAN_PLUS');
  //   const billingType3 = await spaceBilling.getPlan();
  //   expect(billingType3).toBe('PLUS');
  //   // 降成FREE
  //   const subscription4 = await spaceBilling.setPlanByAdmin('FREE', userId);
  //   expect(subscription4.model.product.skuId).toBe('SPACE_PLAN_FREE');
  //   const billingType4 = await spaceBilling.getPlan();
  //   expect(billingType4).toBe('FREE');
});

// test('Billing Plan Switch / Upgrade / Downgrade Test', async () => {
//   const {
//     user: userSO,
//     space: spaceSO,
//     member: _memberSO,
//     rootFolder: _rootFolder,
//   } = await MockContext.initUserContext();
//   const userId = userSO.id;
//   const _spaceId = spaceSO.id;

//   const spaceBilling = spaceSO.billing;

//   // 这次发100000BKC，随便花了！10w (注意，这里我充值了Currency类型的Coin)
//   const bkcAccount = await userSO.coins.getAccount();
//   await bkcAccount.addTransaction(BigInt(100000), 'CREDIT', 'EARN', {
//     reason: 'gm',
//     description: 'Billing Upgrade/Downgrade Test Credit Coin, 100000BKC',
//   });
//   await bkcAccount.refetch();
//   expect(bkcAccount.balance).toBe(BigInt(100100));

//   // 先年付
//   // const calcPlusYear = await spaceBilling.calcUpgradePlanPrice(userId, 'PLUS', 'year', 'USD', true);
//   //

//   // Bika Coin快速升级完毕
//   const upgrade1 = await spaceBilling.upgradePlanWithBikaCoin(userId, 'PLUS', 'year', 'USD');
//   // 没有花真钱
//   expect(upgrade1.payment.checkoutPrice.amount).toBe(0);
//   // 但花了credit coins
//   expect(upgrade1.payment.checkoutPrice.byCoins).toBe(usdPlusYearly * 12); // US$0.99 * 12
//   const subscription = await spaceBilling.getCurrentSubscription();
//   const skuConfig = subscription!.skuConfig as BillingSpacePlanSku;
//   expect(skuConfig.spec).toBe('PLUS');
// });
