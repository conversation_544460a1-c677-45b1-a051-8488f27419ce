'use client';

import CircularProgress from '@mui/joy/CircularProgress';
import mime from 'mime-types';
import React, { useRef, useState } from 'react';
import { match, P } from 'ts-pattern';
import { useTRPCQuery } from '@bika/api-caller';
import { useLocale } from '@bika/contents/i18n/context';
import { utils } from '@bika/domains/shared/client';
import { DatabaseImportExcelPreviewVO } from '@bika/types/database/vo';
import { useSpaceContextForce } from '@bika/types/space/context';
import { Button } from '@bika/ui/button-component';
import DownloadOutlined from '@bika/ui/icons/components/download_outlined';
import ImportOutlined from '@bika/ui/icons/components/import_outlined';
import { Modal } from '@bika/ui/modal-component';
import { useSnackBar } from '@bika/ui/snackbar';
import { NavHeader } from '@bika/ui/web-layout';
import { PreviewTable } from './preview-table';
import { SpaceModalConfig } from '../../../space/client/modals';
import { uploadFile } from '../import-bika-file/axios';

interface IProps {
  databaseId: string;
}

function convertOrigin(url: string) {
  if (url.startsWith('/')) {
    return `${window.location.origin}${url}`;
  }
  return url;
}

type ImportStatus =
  | { status: 'preview'; data: DatabaseImportExcelPreviewVO; file: File }
  | { status: 'parsing' }
  | { status: 'uploading'; file: File }
  | { status: 'success'; columns: number; rows: number }
  | { status: 'error'; message: string };

export const ImportExcelAndIncrement: React.FC<IProps> = ({ databaseId }) => {
  const [isDropOver, setIsDropOver] = useState(false);

  const [importStatus, setImportStatus] = useState<ImportStatus | null>(null);
  const [tmpAttachmentId, setTmpAttachmentId] = useState<string | null>(null);

  const [isSubmitting, setIsSubmitting] = useState(false);

  const { toast } = useSnackBar();
  const [hidden, setHidden] = useState<boolean>(false);

  const inputRef = useRef<HTMLInputElement | null>(null);
  const pasteRef = useRef<HTMLDivElement>(null);
  const { t } = useLocale();
  const spaceContext = useSpaceContextForce();
  const trpcQuery = useTRPCQuery();

  const generateImportExcelTemplate = trpcQuery.database.generateImportExcelTemplate.useQuery(
    { databaseId },
    { enabled: false }, // 一开始不请求
  );
  const getPresignedPut = trpcQuery.attachment.getPresignedPut.useMutation();
  const importFromTemplateExcelPreview = trpcQuery.database.importFromTemplateExcelPreview.useMutation();
  const importFromTemplateExcel = trpcQuery.database.importFromTemplateExcel.useMutation();

  // 下载模版
  const downloadTemplate = async () => {
    const { data: downloadUrl } = await generateImportExcelTemplate.refetch();
    if (downloadUrl) {
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = 'template.xlsx';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      toast(t.global.dl_link_unavailable, { variant: 'error' });
    }
  };

  // 预览数据
  const previewData = async (file: File) => {
    try {
      setImportStatus({ status: 'parsing' });
      const formData = new FormData();
      formData.append('file', file);
      setTmpAttachmentId(null);

      // 1. 获取预签名上传地址
      const { id, presignedPutUrl } = await getPresignedPut.mutateAsync({
        spaceId: spaceContext.data.id,
        contentType: mime.lookup(file.name) || 'application/octet-stream',
        size: file.size,
      });

      // 2. 上传文件
      await uploadFile(convertOrigin(presignedPutUrl), file);
      setTmpAttachmentId(id);

      // 3. 请求预览
      const data = await importFromTemplateExcelPreview.mutateAsync({
        databaseId,
        tmpAttachmentId: id,
      });
      setImportStatus({ status: 'preview', data, file });
    } catch (error) {
      setImportStatus({ status: 'error', message: (error as Error).message });
    }
  };

  // 上传数据文件
  const importData = (file: File) => {
    // setImportStatus({ status: 'uploading', file });
    const formData = new FormData();
    formData.append('file', file);

    setIsSubmitting(true);
    setHidden(true);

    // 背景， mutate 结束即收到sse event ， 所以需要先隐藏modal
    const ele = document.querySelector('.MuiModal-root') as HTMLElement;
    ele.style.setProperty('display', 'none');
    importFromTemplateExcel.mutate(
      {
        id: databaseId,
        tmpAttachmentId: tmpAttachmentId!,
      },
      {
        onSuccess: () => {
          spaceContext.showUIModal(null);
        },
        onError: (error) => {
          setHidden(false);
          setIsSubmitting(false);
          setImportStatus({ status: 'error', message: error.message });
        },
      },
    );
  };

  const handleDrop = (e: React.DragEvent) => {
    setIsDropOver(false);
    e.preventDefault();
    const files = e.dataTransfer.files;
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'text/csv', // .csv
    ];
    const validFiles = Array.from(files).filter(
      (file) => allowedTypes.includes(file.type) || file.name.endsWith('.xlsx') || file.name.endsWith('.csv'),
    );
    if (validFiles.length > 0) {
      previewData(validFiles[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    setIsDropOver(true);
    e.preventDefault();
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    pasteRef.current?.blur();
    const { files } = e.target;
    if (!files) return;
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'text/csv', // .csv
    ];
    const validFiles = Array.from(files).filter(
      (file) => allowedTypes.includes(file.type) || file.name.endsWith('.xlsx') || file.name.endsWith('.csv'),
    );
    e.target.value = '';
    if (validFiles.length > 0) {
      previewData(validFiles[0]);
    }
  };

  const handleClose = () => {
    if (importStatus?.status === 'uploading') {
      Modal.show({
        type: 'warning',
        title: t.resource.cancel_incremental_import,
        content: t.resource.cancel_incremental_import_description,
        onOk: () => {
          spaceContext.showUIModal(null);
        },
      });
    } else {
      spaceContext.showUIModal(null);
    }
  };

  if (hidden) {
    return null;
  }

  return (
    <>
      <SpaceModalConfig showClose={false} width={'560px'} />
      {importStatus?.status !== 'preview' && (
        <NavHeader onClose={() => handleClose()}>{t.resource.incremental_import_from_excel}</NavHeader>
      )}
      {importStatus?.status === 'preview' && (
        <NavHeader onClose={() => handleClose()}>{t.space.preview_import}</NavHeader>
      )}
      <div className="min-h-[430px] flex flex-col">
        {match(importStatus)
          .with({ status: 'success' }, ({ columns, rows }) => (
            <div className="flex-1 flex flex-col justify-center items-center space-y-4">
              <SpaceModalConfig showClose={false} width={'560px'} />
              <CircularProgress determinate value={100} color="success" size="lg">
                Done
              </CircularProgress>
              <div>{t('resource.success_import_excel', { columns, rows })}</div>
              <Button
                color="primary"
                onClick={() => {
                  window.location.href = `/space/${spaceContext.data.id}/node/${databaseId}`;
                }}
              >
                {t.resource.success_import_excel_button}
              </Button>
            </div>
          ))
          .with({ status: 'error' }, ({ message }) => (
            <div className="flex-1 flex flex-col justify-center items-center space-y-4">
              <CircularProgress determinate value={100} color="danger" size="lg">
                Failed
              </CircularProgress>
              <div>{t('resource.error_import_excel', { message })}</div>
              <Button color="primary" onClick={() => setImportStatus(null)}>
                {t.resource.error_import_excel_button}
              </Button>
            </div>
          ))
          .with({ status: 'parsing' }, () => (
            <div className="flex-1 flex flex-col justify-center items-center space-y-4">
              <CircularProgress />
              <div>{t.resource.parsing_excel_data}</div>
            </div>
          ))
          .with({ status: 'uploading' }, ({ file }) => (
            <div className="flex-1 flex flex-col justify-center items-center space-y-4">
              <CircularProgress />
              <div>{file.name}</div>
            </div>
          ))
          .with({ status: 'preview' }, ({ data, file }) => (
            <div className="flex-1">
              <PreviewTable
                data={data}
                file={file}
                isSubmitting={isSubmitting}
                databaseId={databaseId}
                count={data?.total}
                onImport={importData}
                onCancel={() => setImportStatus(null)}
              />
            </div>
          ))
          .with(P.nullish, () => (
            <div className="flex-1 flex flex-col">
              <div className="text-b3 text-[--text-secondary]">{t.resource.import_excel_step1}</div>
              <div className="mt-2 mb-6">
                <Button
                  color="primary"
                  startDecorator={<DownloadOutlined />}
                  sx={{ height: '32px' }}
                  onClick={downloadTemplate}
                >
                  {t.resource.download_template}
                </Button>
              </div>
              <div className="text-b3 text-[--text-secondary]">{t.resource.import_excel_step2}</div>
              <div className="flex-1 flex flex-col justify-center">
                <input defaultValue="" hidden type="file" ref={inputRef} onChange={handleChange} accept=".xlsx,.csv" />
                <div
                  className={utils.cn(
                    'border-[1px] border-[--border-default] border-dashed space-y-2 w-full h-[260px] flex flex-col justify-center items-center space-x-1 rounded focus:border-[--brand] text-[--text-secondary] outline-none',
                    isDropOver && 'border-[--brand]',
                  )}
                  tabIndex={1}
                  onDrop={handleDrop}
                  onDragOver={handleDragOver}
                  onDragLeave={() => setIsDropOver(false)}
                  ref={pasteRef}
                >
                  <ImportOutlined color={'var(--text-secondary)'} size={32} />
                  <div className="flex justify-center items-center">
                    <p onClick={() => inputRef.current?.click()} className={'text-[--brand] cursor-pointer'}>
                      {t.record.add_local_file}
                    </p>
                    &nbsp;{t.auth.or}&nbsp;
                    <p>{t.record.paste_or_drop_file_upload}</p>
                  </div>
                  <p>{t.resource.support_upload_file}</p>
                </div>
              </div>
            </div>
          ))
          .exhaustive()}
      </div>
    </>
  );
};
