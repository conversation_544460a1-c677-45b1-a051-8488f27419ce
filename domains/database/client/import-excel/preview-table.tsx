import React, { useMemo } from 'react';
import { useLocale } from '@bika/contents/i18n/context';
import { DatabaseVOProvider } from '@bika/types/database/context';
import {
  CellRenderVO,
  CellValueVO,
  DatabaseImportExcelPreviewVO,
  RecordRenderVO,
  ViewVO,
} from '@bika/types/database/vo';
import { useSpaceContextForce } from '@bika/types/space/context';
import { Button } from '@bika/ui/button';
import { SpaceModalConfig } from '../../../space/client/modals';
import { BGrid } from '../table-view/ag-grid/bgrid';

interface IProps {
  count?: number;
  data: DatabaseImportExcelPreviewVO;
  file: File;
  databaseId: string;
  onImport: (file: File) => void;
  isSubmitting?: boolean;
  onCancel: () => void;
}

export const PreviewTable: React.FC<IProps> = ({
  data,
  file,
  databaseId,
  count,
  onImport,
  onCancel,
  isSubmitting = false,
}) => {
  const spaceContext = useSpaceContextForce();
  const { t } = useLocale();

  const view = useMemo<ViewVO>(
    () => ({
      id: databaseId,
      name: 'ALL',
      // type: 'TABLE',
      type: 'TABLE',
      databaseId,
      columns: data?.fields.map((item) => ({
        ...item,
        name: item.name,
      })),
    }),
    [data, databaseId],
  );

  // console.log('view', view);

  const getData = useMemo(
    () => async (_params: { startRow: number | undefined; endRow: number | undefined; keyword?: string }) => {
      const rows: RecordRenderVO[] = data?.records.slice(0, 10).map((row, index) => {
        const cells = Object.entries(row.data)
          .map(([fieldId, cellValue]) => ({
            value: cellValue,
            data: cellValue,
            id: fieldId,
          }))
          .reduce<Record<string, CellRenderVO>>((acc, cur) => {
            acc[cur.id] = {
              id: cur.id,
              data: cur.data,
              value: cur.value as CellValueVO,
            };
            return acc;
          }, {});
        return {
          id: index.toString(),
          databaseId,
          revision: 1,
          cells,
        };
      });

      return {
        total: Math.min(data?.records.length, 10),
        rows,
      };
    },
    [data, databaseId],
  );

  const databaseVO = useMemo(
    () => ({
      id: databaseId,
      name: t.pricing.oncely.oncely_code_management,
      spaceId: spaceContext.data.id,
      views: [view],
    }),
    [view, databaseId, spaceContext.data.id, t],
  );

  return (
    <div className="h-[720px] flex flex-col">
      <SpaceModalConfig showClose={false} width={'1080px'} />

      <div className="[&>div]:!h-full flex-1 block">
        <DatabaseVOProvider
          value={{
            value: databaseVO,
            getView: () => ({ data: view, isLoading: false, refetch: () => {} }),
            updateView: async () => {},
            getRecord: () => null!,
            listRecords: getData,
            getLinkDatabase: () => ({
              data: {} as unknown as Record<
                string,
                {
                  id: string;
                  name: string;
                  spaceId: string;
                  views: [];
                }
              >,
              isLoading: false,
              isFetching: false,
            }),
            useParams: () => ({ databaseId }),
            useRecordMutation: () => ({
              createRecord: async () => null!,
              updateRecord: async () => null!,
              updateRecords: async () => null!,
              deleteRecords: async () => {},
              isMutating: () => false,
            }),
          }}
        >
          {view && (
            <BGrid
              spaceId={spaceContext.data.id}
              databaseId={databaseId}
              disableEditing
              disableOperateColumn
              onUpdateCustomFields={() => {}}
              tipSharingLogin={() => {}}
              sorts={[]}
              onSort={() => {}}
              onPaging={() => {}}
              view={view}
            />
          )}
        </DatabaseVOProvider>
      </div>
      <div className="text-b3 text-[--text-secondary] mt-2">
        {t('resource.import_excel_records_count', {
          count: count ?? data?.records.length.toLocaleString('en-us') ?? 0,
        })}
      </div>
      <div className="flex justify-center mt-4 space-x-6">
        <Button sx={{ height: '40px' }} onClick={() => onImport(file)} loading={isSubmitting}>
          {t.resource.import_excel_import_button}
        </Button>
        <Button sx={{ height: '40px' }} onClick={onCancel} variant="soft" color="neutral">
          {t.cancel}
        </Button>
      </div>
    </div>
  );
};
