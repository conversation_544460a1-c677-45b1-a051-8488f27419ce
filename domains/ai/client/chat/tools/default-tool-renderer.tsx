import type { ToolInvocation } from '@ai-sdk/ui-utils';
import React from 'react';
import type { ILocaleContext } from '@bika/contents/i18n/context';
import type { ToolUIContentProps, ToolUIComponentProps } from '@bika/domains/ai-skillset/types';
import type { INodeIconValue } from '@bika/types/node/bo';
import { IconButton, Button } from '@bika/ui/button';
import CheckCircleOutlined from '@bika/ui/icons/components/check_circle_outlined';
import CloseCircleOutlined from '@bika/ui/icons/components/close_circle_outlined';
import ExpandOutlined from '@bika/ui/icons/components/expand_outlined';
import NarrowOutlined from '@bika/ui/icons/components/narrow_outlined';
import { Stack, Box } from '@bika/ui/layouts';
import { NodeIcon } from '@bika/ui/node/icon';
import { EllipsisText } from '@bika/ui/text';
import { Typography } from '@bika/ui/texts';
import { TOOL_RESULT_CANCELED, TOOL_RESULT_APPLIED } from './type';
import { CONST_DEFAULT_SKILL_NODE_ICON } from '../../../../ai-skillset/default/client';

export type AnyToolProps = ToolUIComponentProps & {
  contentProps: ToolUIContentProps;
};
export function getDefaultToolUIContentProps(
  toolInvocation: ToolInvocation,
  { locale, displayName }: { displayName?: string; locale: ILocaleContext },
): ToolUIContentProps {
  // 默认的工具内容属性
  const defaultToolContentProps: ToolUIContentProps = {
    displayName,
    call: {
      name: displayName || toolInvocation.toolName,
      description: locale.t.ai.in_progress,
    },
    result: {
      name: displayName || toolInvocation.toolName,
      description: locale.t.ai.completed,
    },
  };
  return defaultToolContentProps;
}

export const DefaultToolRenderer = (props: AnyToolProps) => {
  const { i, t } = props.localeContext;
  const { contentProps, toolInvocation, skillsets } = props;
  const [isApplyLoading, setIsApplyLoading] = React.useState(false);

  // const isConsulting = toolInvocation.toolName.startsWith('ai-consulting-');
  const isCall = toolInvocation.state === 'call';
  const isResult = toolInvocation.state === 'result';
  const isAsk = contentProps?.isAsk;

  // 判断是否需要审批
  const needApproval =
    skillsets?.some(
      (skillset) => 'needApprovals' in skillset && skillset.needApprovals?.includes(toolInvocation.toolName),
    ) || isAsk;

  return (
    <Stack direction="row" alignItems="center">
      {/* {isConsulting && (
        <Box
          sx={
            props.hideFlow
              ? {}
              : {
                  position: 'relative',
                  '&::before': {
                    content: '""',
                    width: '1px',
                    height: '80px',
                    position: 'absolute',
                    left: '10px',
                    bottom: '20px',
                    borderLeft: '1px solid var(--border-default)',
                  },
                }
          }
        >
          <Box
            sx={{
              width: '20px',
              height: '20px',
              borderRadius: '50%',
              border: '1px solid var(--border-default)',
              mr: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <svg width="10" height="7" viewBox="0 0 10 7" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M9 1L4 6L1 3"
                stroke="var(--status-success)"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </Box>
        </Box>
      )} */}
      <Box
        sx={{
          border: `1px solid ${props.isHighlight ? 'var(--brand)' : 'var(--border-default)'}`,
          backgroundColor: 'var(--bg-surface)',
          borderRadius: '8px',
          maxWidth: '400px',
          width: '100%',
        }}
      >
        <Stack
          direction="row"
          alignItems="center"
          sx={{
            p: 1,
            gap: 1,
            backgroundColor: 'var(--bg-surface)',
            position: 'relative',
            overflow: 'hidden',
            flex: 1,
            borderRadius: '8px',
          }}
        >
          {isCall && !needApproval && (
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: '-100%',
                width: '100%',
                height: '100%',
                background: 'linear-gradient(90deg, transparent 0%, var(--brand-light) 50%, transparent 100%)',
                animation: 'progressAnimation 2s infinite linear',
                pointerEvents: 'none',
                '@keyframes progressAnimation': {
                  '0%': {
                    left: '-100%',
                  },
                  '100%': {
                    left: '100%',
                  },
                },
              }}
            />
          )}
          {(() => {
            if (contentProps?.nodeType) {
              return <NodeIcon size={32} value={{ kind: 'node-resource', nodeType: contentProps?.nodeType }} />;
            }
            if (contentProps.icon && 'kind' in contentProps.icon) {
              return <NodeIcon value={contentProps.icon} />;
            }
            return contentProps.icon;
          })()}
          <Box sx={{ flex: 1, minWidth: 0, position: 'relative' }}>
            <Stack direction="row" justifyContent="space-between" alignItems="center" gap={1}>
              {toolInvocation.state === 'call' && (
                <Box overflow={'hidden'} sx={{ flex: 1, minWidth: 0 }}>
                  <>
                    {/* 判断是否是 needApprovals?  寻找 skillsets，遍历其子元素的 needApprovals 里是否包含对应 toolName */}

                    <>
                      <Typography textColor={'var(--text-primary)'} level="b2">
                        {i(contentProps.call?.name)}
                      </Typography>
                      <EllipsisText>
                        <Typography textColor={'var(--text-secondary)'} level="b4" noWrap>
                          {i(contentProps.call?.description)}
                        </Typography>
                      </EllipsisText>
                    </>
                  </>
                </Box>
              )}

              {isResult && (
                <Box overflow={'hidden'} sx={{ flex: 1, minWidth: 0 }}>
                  <>
                    <Typography textColor={'var(--text-primary)'} level="b2">
                      {i(contentProps.result?.name)}
                    </Typography>
                    <EllipsisText tooltip={i(contentProps.result?.description)}>
                      <Typography textColor={'var(--text-secondary)'} level="b4" noWrap>
                        {![TOOL_RESULT_CANCELED, TOOL_RESULT_APPLIED].includes(toolInvocation?.result) &&
                          i(contentProps.result?.description)}
                        {toolInvocation.result === TOOL_RESULT_CANCELED && 'Canceled'}
                        {toolInvocation.result === TOOL_RESULT_APPLIED && 'Applied'}
                      </Typography>
                    </EllipsisText>
                  </>
                </Box>
              )}

              <Box sx={{ flexShrink: 0 }}>
                <IconButton
                  onClick={() => {
                    if (props.isHighlight) {
                      props.onCloseTool?.();
                      return;
                    }
                    props.onClickTool?.();
                  }}
                >
                  {props.isHighlight ? (
                    <NarrowOutlined size={16} color="var(--text-secondary)" />
                  ) : (
                    <ExpandOutlined size={16} color="var(--text-secondary)" />
                  )}
                </IconButton>
              </Box>
            </Stack>
            {/* {isConsulting && (
              <Typography
                textColor="var(--text-secondary)"
                sx={{
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  maxWidth: '320px',
                  mt: 1,
                }}
              >
                {JSON.stringify((toolInvocation as any).result)}
              </Typography>
            )} */}
          </Box>
        </Stack>
        {needApproval &&
          !(isResult && [TOOL_RESULT_CANCELED, TOOL_RESULT_APPLIED].includes(toolInvocation?.result)) && (
            <Box
              sx={{
                display: 'flex',
                gap: 2,
                pb: 2,
                px: 2,
              }}
            >
              <Button
                disabled={isResult || isApplyLoading}
                loading={isApplyLoading}
                onClick={async () => {
                  setIsApplyLoading(true);
                  try {
                    const result = await props.executeToolResult(toolInvocation.toolCallId);
                    console.log('Tool result executed:', result);
                    props.addToolResult(result || TOOL_RESULT_APPLIED);
                  } catch (error: unknown) {
                    console.error('Tool result executed error:', error);
                    props.addToolResult({
                      error: {
                        message: error instanceof Error ? error.message : 'Unknown error',
                      },
                    });
                  } finally {
                    setIsApplyLoading(false);
                  }
                }}
                fullWidth
                sx={{
                  '&.Mui-disabled': {
                    backgroundColor: 'var(--brand) !important',
                    opacity: 0.5,
                  },
                }}
                startDecorator={
                  <CheckCircleOutlined
                    color={
                      isResult || isApplyLoading
                        ? 'color-mix(in srgb, var(--static) 50%, transparent);'
                        : 'var(--static)'
                    }
                  />
                }
              >
                {t.action.apply}
              </Button>
              <Button
                disabled={isResult || isApplyLoading}
                onClick={() => {
                  props.addToolResult(TOOL_RESULT_CANCELED);
                }}
                fullWidth
                startDecorator={
                  <CloseCircleOutlined
                    color={isResult || isApplyLoading ? 'var(--text-disabled)' : 'var(--text-primary)'}
                  />
                }
                variant="outlined"
                color="neutral"
              >
                {t.action.cancel}
              </Button>
            </Box>
          )}
      </Box>
    </Stack>
  );
};

export const ToolErrorRenderer = (
  props: AnyToolProps & {
    error: string;
  },
) => {
  const { error, contentProps, toolInvocation } = props;
  const { i } = props.localeContext;

  let name = '';
  if (toolInvocation.state === 'result') {
    name = i(contentProps?.result?.name);
  } else if (toolInvocation.state === 'call') {
    name = i(contentProps?.call?.name);
  }

  return (
    <Stack
      direction="row"
      alignItems="center"
      sx={{
        border: '1px solid var(--status-danger)',
        backgroundColor: 'var(--bgDangerLightDefault)',
        borderColor: 'var(--status-danger)',
        p: 1,
        borderRadius: '8px',
        gap: 1,
        maxWidth: '400px',
        mb: 1,
      }}
    >
      <Box sx={{ flexShrink: 0 }}>
        {React.isValidElement(contentProps.icon) ? (
          contentProps.icon
        ) : (
          <NodeIcon value={(contentProps.icon as INodeIconValue) || CONST_DEFAULT_SKILL_NODE_ICON} />
        )}
      </Box>
      <Box overflow={'hidden'}>
        <Typography textColor={'var(--status-danger)'} level="b2">
          {name}
        </Typography>
        <Typography textColor={'var(--status-danger)'} level="b4" overflow={'hidden'} textOverflow={'ellipsis'}>
          {error}
        </Typography>
      </Box>
    </Stack>
  );
};
