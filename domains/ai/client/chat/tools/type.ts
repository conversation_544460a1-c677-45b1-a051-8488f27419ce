import type { ToolInvocationUIPart, ToolInvocation } from '@ai-sdk/ui-utils';
import type { AIIntentUIResolveDTO } from '@bika/types/ai/dto';
import type { IToolUIShared } from '../../../../ai-skillset/types';

export type ToolUIProps = IToolUIShared & {
  part: ToolInvocationUIPart;
  disabled: boolean;

  sendUI: (uiResolve: AIIntentUIResolveDTO) => Promise<void>;
  sendMessage: (message: string) => Promise<void>;
  onClickTool: (uiPart: ToolInvocation) => void;
  onCloseTool?: () => void;
  // skillsets: string[] | undefined;
  error: string | undefined;
};

export const TOOL_RESULT_CANCELED = 'CANCELED';
export const TOOL_RESULT_APPLIED = 'APPLIED';

export interface ToolResultVO {
  error?: {
    message: string;
  };
}
export type ToolResultWithError<T> = ToolResultVO & T;
