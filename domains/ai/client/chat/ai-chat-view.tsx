import type { Message } from '@ai-sdk/ui-utils';
import { useSearchParams, useRouter } from 'next/navigation';
import React from 'react';
import { useApiCaller } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n/context';
import { useAIChatSession } from '@bika/domains/ai/client/chat/hooks/use-ai-chat-cache';
import type { AIIntentParams, AIChatInputConfig } from '@bika/types/ai/bo';
import { toAISDKMessage } from '@bika/types/ai/bo';
import type { IAIChatInputStateContext } from '@bika/types/ai/context';
import type { AIWizardVO } from '@bika/types/ai/vo';
import type { ISpaceContext } from '@bika/types/space/context';
import type { Locale } from '@bika/types/system';
import { useGlobalState } from '@bika/types/website/context';
import { Button } from '@bika/ui/button-component';
import AddOutlined from '@bika/ui/icons/components/add_outlined';
import { Skeleton } from '@bika/ui/skeleton';
import { snackbarShow } from '@bika/ui/snackbar';
import { type AIChatHandle, AIChatUIRenderer } from './ai-chat-ui';

export interface AIChatViewProps {
  inputState: IAIChatInputStateContext;
  config?: AIChatInputConfig;

  // initUserMessage?: string;
  // use previous dialog, or new dialog?
  // initChatId?: string;
  // if new dialog, what's the intent type?
  initAIIntent?: AIIntentParams;

  onClose?: () => void;

  // 是否内置modal component
  withModal?: boolean;
  // 是否强制语言？会在 useChat的 custom body 传入
  forceLocale?: Locale;
  // 是否侧边栏模式
  displayMode?: 'COPILOT' | 'MODAL' | 'VIEW';

  // context?: AIChatContextVO[];
  // allowContextMenu?: AIChatContextVO['type'][];
  setStage?: (stage: 'welcome' | 'chat') => void;
  skillsetIcons?: React.ReactNode;
}

/**
 * 使用Streaming AI Chat View 的Wizard2
 *
 * @returns
 */
export function AIChatView(props: AIChatViewProps) {
  const { setStage, inputState } = props;
  const { trpc } = useApiCaller();
  const { lang, t } = useLocale();
  const chatUIHandle = React.useRef<AIChatHandle>(null);
  const [globalSpaceContext] = useGlobalState<ISpaceContext>('SPACE');
  const [wizard, setWizard] = React.useState<AIWizardVO | null>(null);
  const searchParams = useSearchParams();
  const router = useRouter();
  const spaceId = globalSpaceContext?.data.id;
  const isCopilot = props.initAIIntent?.type === 'COPILOT';

  const { session: spaceSession, isLoading: isLoadingSession } = useAIChatSession({
    type: 'space',
    spaceId: spaceId!,
  });

  const [initError, setInitError] = React.useState<string | null>(null);

  const isWizardFetching = React.useRef(false);
  const newWizardCreatedRef = React.useRef(false);

  // init start message 是否已经被执行完毕
  const initStartMessageExecuted = React.useRef(false); // 这里有 ref，是避免 useEffect 开发环境下的副作用执行两次

  const initDialog = React.useCallback(
    async (wizardId?: string) => {
      isWizardFetching.current = true;
      if (wizardId) {
        const data = await trpc.ai.fetchWizard.query({ wizardId });
        inputState.setChatId(data.id);
        isWizardFetching.current = false;
        setWizard(data as AIWizardVO);
        setInitError(null);
      } else {
        if (newWizardCreatedRef.current) {
          isWizardFetching.current = false;
          return;
        }
        newWizardCreatedRef.current = true;
        const data = await trpc.ai.newWizard.query({
          spaceId: spaceId!,
          intent: props.initAIIntent || {
            type: 'SEARCH', // default ai intent
          },
        });
        snackbarShow({
          content: t.wizard.new_wizard_created,
          color: 'success',
        });
        inputState.setChatId(data.id);
        isWizardFetching.current = false;
        setWizard(data as AIWizardVO);
        setInitError(null);
      }
    },
    [spaceId],
  );

  React.useEffect(() => {
    if (isCopilot && !inputState.chatId) {
      newWizardCreatedRef.current = false;
    }
  }, [isCopilot, inputState.chatId]);

  // 初始化 Dialog
  React.useEffect(() => {
    const fetchWizard = async () => {
      try {
        await initDialog(inputState.chatId);
      } catch (error) {
        isWizardFetching.current = false;
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (errorMessage.includes('not found') && setStage) {
          // 清除缓存的 chatId
          inputState.clear();
          setStage('welcome'); // 切换到欢迎界面
          return;
        }
        setInitError(errorMessage);
      }
    };
    // fetch 时会销毁 AIChatUIRenderer 导致 doAppendMessage 失效
    if ((!inputState.chatId || wizard?.id !== inputState.chatId) && !isWizardFetching.current) {
      fetchWizard();
    }
  }, [inputState.chatId, wizard?.id, isWizardFetching.current]);

  React.useEffect(() => {
    const currentChat = searchParams.get('currentChat');
    if (wizard && chatUIHandle.current && !initStartMessageExecuted.current && currentChat === 'true') {
      chatUIHandle.current.doAppendMessage(inputState.input ?? '', inputState.contexts || []);

      initStartMessageExecuted.current = true;
      const newSearchParams = new URLSearchParams(searchParams.toString());
      newSearchParams.delete('currentChat');
      router.replace(`${window.location.pathname}?${newSearchParams.toString()}`, { scroll: false });
    }
  }, [initStartMessageExecuted, searchParams, wizard, inputState]);

  // 拿到 space chat 缓存，并且 url 有 chat=true 的时候，自动发送一条消息
  React.useEffect(() => {
    // 处理 space home 跳过来的逻辑
    const chat = searchParams.get('chat');
    if (wizard && chatUIHandle.current && !initStartMessageExecuted.current && chat === 'true' && !isLoadingSession) {
      chatUIHandle.current.doAppendMessage(spaceSession.input || '', spaceSession.contexts || []);

      initStartMessageExecuted.current = true;
      const newSearchParams = new URLSearchParams(searchParams.toString());
      newSearchParams.delete('chat');
      router.replace(`${window.location.pathname}?${newSearchParams.toString()}`, { scroll: false });

      // 清除 space session 的缓存
      spaceSession.clear();
    }
    // 如果有队列 queue，也么上发送一条消息
  }, [initStartMessageExecuted, searchParams, wizard, isLoadingSession, spaceSession]);

  // 初始化显示的消息
  const initialMessages = React.useMemo(() => {
    const msgs: Message[] = [];
    if (wizard) {
      let i = 0;
      for (const msg of wizard.messages) {
        const aiSDKMessage = toAISDKMessage(msg);

        msgs.push(aiSDKMessage);
        i++;
      }
    }
    return msgs;
  }, [wizard, lang, props.forceLocale]);

  if (initError) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <div className="text-red-500 mb-4">{initError}</div>
        <Button
          onClick={() => {
            initDialog();
          }}
          startDecorator={<AddOutlined color="var(--text-secondary)" />}
        >
          {t.ai.new_chat}
        </Button>
      </div>
    );
  }

  if (isWizardFetching.current || !wizard) return <Skeleton pos="CHAT" />;

  return (
    <>
      <AIChatUIRenderer
        inputState={inputState}
        ref={chatUIHandle}
        // options={wizard.options}
        displayMode={props.displayMode}
        config={{ ...props.config, options: [...(props.config?.options || []), ...(wizard.options || [])] }}
        disabled={wizard.resolutionStatus === 'SUCCESS'}
        api={'/api/ai/chat'}
        initialMessages={initialMessages}
        skillsetIcons={props.skillsetIcons}
        onFinish={(_message) => {
          // trpc.ai.fetchWizard.query({ wizardId: wizard.id }).then((data) => setWizard(data as AIWizardVO));
        }}
        // textFilter={(text) => {
        //   if (text.startsWith('/resolve:')) {
        //     return `🖱️🖱️🖱️`; //  ${text}`;
        //   }
        //   return text;
        // }}
        chatId={wizard.id}
        customBody={
          wizard
            ? {
                forceLocale: props.forceLocale,
                // initUserMessage: props.initUserMessage,
                // use previous dialog, or new dialog?
                // if new dialog, what's the intent type?
                initAIIntent: props.initAIIntent,
              }
            : undefined
        }
      />
    </>
  );
}
