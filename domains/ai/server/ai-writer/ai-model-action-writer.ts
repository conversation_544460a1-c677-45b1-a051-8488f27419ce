import assert from 'assert';
import { AIWriterResponse } from '@bika/types/ai/bo';
import { BaseAIWriter } from './base-ai-writer';
import { AISO } from '../ai-so';

export class AIModelActionAIWriter extends BaseAIWriter {
  async write(userPrompt: string): Promise<AIWriterResponse> {
    assert(this._writerOptions.type === 'AI_MODEL_ACTION', 'AIWriter type must be AI_MODEL_ACTION');

    const { model } = this._writerOptions;

    const { result } = await AISO.streamText(
      {
        user: this._user,
        prompt: userPrompt,
      },
      {
        model,
      },
    );

    for await (const chunk of result.fullStream) {
      if (chunk.type === 'error') {
        throw chunk.error;
      }
    }

    const data = await result.text;
    return {
      data,
      success: true,
      message: 'success',
    };
  }
}
