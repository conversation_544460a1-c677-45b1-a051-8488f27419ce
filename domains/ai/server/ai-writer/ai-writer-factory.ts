import { IAIWriterContext } from '@bika/contents/config/client/ai/ai-writer/ai-writer';
import type { UserSO } from '@bika/domains/user/server/user-so';
import type { AIWriterType, AIWriter } from '@bika/types/ai/bo';
import { AIModelActionAIWriter } from './ai-model-action-writer';
import { BaseAIWriter } from './base-ai-writer';
import { FolderREADMEAIWriter } from './folder-readme-writer';
import { I18NStringAIWriter } from './i18n-string-ai-writer';
import { RecordCellFillingAIWriter } from './record-cell-filling-writer';
import { RecordWriter } from './record-writer';
import { ResourceDescriptionWriter } from './res-description-writer';
import { TextGenerationAIWriter } from './text-generation-writer';

export const AIWriterFactoryMap: Record<
  AIWriterType,
  new (writerOptions: AIWriter, context: IAIWriterContext, user: UserSO) => BaseAIWriter
> = {
  AI_MODEL_ACTION: AIModelActionAIWriter,
  TEXT_GENERATION: TextGenerationAIWriter,
  TEXT_REPHRASE: TextGenerationAIWriter,
  I18N_STRING: I18NStringAIWriter,
  RESOURCE_README: FolderREADMEAIWriter,
  RESOURCE_DESCRIPTION: ResourceDescriptionWriter,
  TRANSLATE: FolderREADMEAIWriter,
  EXTRACT_TO_RECORD: RecordWriter,
  GENERATE_MOCK_RECORD: RecordWriter,
  RECORD_CELL_FILLING: RecordCellFillingAIWriter,
};

export function newAIWriter(
  type: AIWriterType,
  writerOptions: AIWriter,
  context: IAIWriterContext,
  user: UserSO,
): BaseAIWriter {
  const CLASS = AIWriterFactoryMap[type];

  const writer = new CLASS(writerOptions, context, user);

  return writer;
}
