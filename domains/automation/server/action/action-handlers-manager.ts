// import { FormAppAIActionHandler } from '@bika/domains/automation-nodes/formapp-action/server';
import { ToolSDKAIActionHandler } from '@bika/domains/automation-nodes/toolsdk-action/server';
import { ActionType, ActionTypeSchema } from '@bika/types/automation/bo';
import { IAction } from './abstract-action-handler';
import { AIModelActionHandler } from './ai-model-action-handler';
import { AISummaryActionHandler } from './ai-summary-action-handler';
import { CallAgentActionHandler } from './call-agent-action-handler';
import { CreateDocumentActionHandler } from './create-document-action-handler';
import { CreateMissionActionHandler } from './create-mission-action-handler';
import { CreateRecordActionHandler } from './create-record-action-handler';
import { DelayActionHandler } from './delay-action-handler';
import { FilterActionHandler } from './filter-action-handler';
import { FindDashboardActionHandler } from './find-dashboard-action-handler';
import { FindMembersActionHandler } from './find-members-action-handler';
import { FindMissionsActionHandler } from './find-missions-action-handler';
import { FindRecordsActionHandler } from './find-records-action-handler';
import { FindWidgetActionHandler } from './find-widget-action-handler';
import { LoopActionHandler } from './loop-action-handler';
import { OpenAIGenerateTextActionHandler } from './openai-generate-text-action-handler';
import { RandomActionHandler } from './random-action-handler';
import { RoundRobinActionHandler } from './round-robin-action-handler';
import { RunScriptActionHandler } from './run-script-action-handler';
import { SendEmailActionHandler } from './send-email-action-handler';
import { SendReportActionHandler } from './send-report-action-handler';
import { TwitterUploadMediaActionHandler } from './twitter-upload-media-action-handler';
import { UpdateRecordActionHandler } from './update-record-action-handler';
import { WebhookActionHandler } from './webhook-action-handler';
import { DingtalkWebhookActionHandler } from './webhook-dingtalk-action-handler';
import { FeishuWebhookActionHandler } from './webhook-feishu-action-handler';
import { SlackWebhookActionHandler } from './webhook-slack-action-handler';
import { TelegramWebhookActionHandler } from './webhook-telegram-action-handler';
import { WecomWebhookActionHandler } from './webhook-wecom-action-handler';
import { XCreateTweetActionHandler } from './x-create-tweet-action-handler';

export class ActionHandlersManager {
  static actionMap = new Map<ActionType, IAction>();

  static registerAction(actionType: ActionType, action: IAction): void {
    this.actionMap.set(actionType, action);
  }

  static getAction(actionType: ActionType): IAction {
    if (!this.actionMap.has(actionType)) {
      throw new Error(`Action handler ${actionType} not found`);
    }
    return this.actionMap.get(actionType)!;
  }
}

// 在初始化时注册触发器
ActionHandlersManager.registerAction(ActionTypeSchema.enum.AI_MODEL, new AIModelActionHandler());
ActionHandlersManager.registerAction('AI_SUMMARY', new AISummaryActionHandler());
ActionHandlersManager.registerAction('CALL_AGENT', new CallAgentActionHandler());
ActionHandlersManager.registerAction('CREATE_MISSION', new CreateMissionActionHandler());
ActionHandlersManager.registerAction('CREATE_RECORD', new CreateRecordActionHandler());
ActionHandlersManager.registerAction('DELAY', new DelayActionHandler());
ActionHandlersManager.registerAction('FILTER', new FilterActionHandler());
ActionHandlersManager.registerAction('FIND_DASHBOARD', new FindDashboardActionHandler());
ActionHandlersManager.registerAction('FIND_MEMBERS', new FindMembersActionHandler());
ActionHandlersManager.registerAction('FIND_MISSIONS', new FindMissionsActionHandler());
ActionHandlersManager.registerAction('FIND_RECORDS', new FindRecordsActionHandler());
ActionHandlersManager.registerAction('FIND_WIDGET', new FindWidgetActionHandler());
// ActionHandlersManager.registerAction(ActionTypeSchema.enum.FORMAPP_AI, new FormAppAIActionHandler());
ActionHandlersManager.registerAction('LOOP', new LoopActionHandler());
ActionHandlersManager.registerAction('RANDOM', new RandomActionHandler());
ActionHandlersManager.registerAction('ROUND_ROBIN', new RoundRobinActionHandler());
ActionHandlersManager.registerAction('RUN_SCRIPT', new RunScriptActionHandler());
ActionHandlersManager.registerAction('SEND_EMAIL', new SendEmailActionHandler());
ActionHandlersManager.registerAction('SEND_REPORT', new SendReportActionHandler());
ActionHandlersManager.registerAction(ActionTypeSchema.enum.TOOLSDK_AI, new ToolSDKAIActionHandler());
ActionHandlersManager.registerAction('UPDATE_RECORD', new UpdateRecordActionHandler());
ActionHandlersManager.registerAction('DINGTALK_WEBHOOK', new DingtalkWebhookActionHandler());
ActionHandlersManager.registerAction('FEISHU_WEBHOOK', new FeishuWebhookActionHandler());
ActionHandlersManager.registerAction('SLACK_WEBHOOK', new SlackWebhookActionHandler());
ActionHandlersManager.registerAction('TELEGRAM_SEND_MESSAGE', new TelegramWebhookActionHandler());
ActionHandlersManager.registerAction('WECOM_WEBHOOK', new WecomWebhookActionHandler());
ActionHandlersManager.registerAction('WEBHOOK', new WebhookActionHandler());
ActionHandlersManager.registerAction('X_CREATE_TWEET', new XCreateTweetActionHandler());
ActionHandlersManager.registerAction('TWITTER_UPLOAD_MEDIA', new TwitterUploadMediaActionHandler());
ActionHandlersManager.registerAction('OPENAI_GENERATE_TEXT', new OpenAIGenerateTextActionHandler());
ActionHandlersManager.registerAction('DEEPSEEK', new OpenAIGenerateTextActionHandler());
ActionHandlersManager.registerAction('CREATE_DOCUMENT', new CreateDocumentActionHandler());
