import { isBlank, isNotBlank } from 'basenext/utils/string';
import { AIWriterSO } from '@bika/domains/ai/server/ai-writer-so';
import { BObuildIdConverter } from '@bika/domains/node/server/bo-builder/bo-build-id-converter';
import { getDefaultLocale, render, replaceVariablesInString } from '@bika/domains/shared/server';
import { Action, AIModelAction, AIModelActionSchema, AIModelActionOutput } from '@bika/types/automation/bo';
import { AbstractActionOmitExportBuilderHandler } from './abstract-action-builder-handler';
import { IActionRunInput, IActionRunContext } from './types';

export class AIModelActionHandler<T extends AIModelAction> extends AbstractActionOmitExportBuilderHandler<T> {
  override desensitizeInput(input: T['input']): T['input'] {
    const { model } = input;
    if (model.kind !== 'custom') {
      return input;
    }
    const { custom } = model;
    if (custom.type === 'integration') {
      return {
        ...input,
        model: {
          ...model,
          custom: {
            ...custom,
            integrationId: custom.integrationId ? '' : undefined,
          },
        },
      };
    }
    if (!custom.provider) {
      return input;
    }
    return {
      ...input,
      model: {
        ...model,
        custom: {
          ...custom,
          provider: {
            ...custom.provider,
            apiKey: custom.provider.apiKey ? '' : undefined,
            organizationId: custom.provider.organizationId ? '' : undefined,
          },
        },
      },
    };
  }

  override convertInput(input: T['input'], converter: BObuildIdConverter): T['input'] {
    const relationFunc = converter.getRelationFunc();

    const prompt = replaceVariablesInString(input.prompt, relationFunc);

    return { ...input, prompt };
  }

  override validateBO(action: Action): boolean {
    const { success } = AIModelActionSchema.safeParse(action);
    if (!success) {
      return false;
    }
    const { model, prompt } = action.input;
    const isBlankPrompt = isBlank(prompt);
    if (isBlankPrompt) {
      return false;
    }
    if (model.kind !== 'custom') {
      return true;
    }
    const { custom } = model;
    if (custom.type === 'integration') {
      return isNotBlank(custom.integrationId);
    }
    if (!custom.provider) {
      return false;
    }
    return isNotBlank(custom.provider.apiKey);
  }

  override async doTest(input: IActionRunInput, context: IActionRunContext): Promise<AIModelActionOutput> {
    return this.fetchRunOutput(input, context);
  }

  override async fetchRunOutput(input: IActionRunInput, context: IActionRunContext): Promise<AIModelActionOutput> {
    const action = AIModelActionSchema.parse(input.action);

    const { model, prompt } = action.input;

    const props = { ...context };

    const userPrompt = render(prompt, props);

    const writerContext = {
      userId: input.userId,
      spaceId: input.spaceId,
      locale: input.locale || getDefaultLocale(),
      createdAt: new Date().toISOString(),
    };
    const user = await super.getOperationUser(input);

    const result = await AIWriterSO.quickWrite(
      {
        type: 'AI_MODEL_ACTION',
        automationId: input.automationId,
        actionId: action.id,
        model,
      },
      userPrompt,
      writerContext,
      user!,
    );
    if (!result.success) {
      throw new Error(result.message);
    }

    return { text: result.data };
  }
}
