{"name": "@toolsdk.ai/domain", "version": "1.9.2-beta.1", "private": true, "description": "", "main": "index.ts", "exports": {".": "./index.ts", "./apis/*": "./apis/*.ts", "./server": "./server/index.ts", "./server/*": "./server/*.ts", "./client": "./client/index.ts", "./client/api": "./client/api.ts", "./client/*": "./client/*.tsx"}, "dependencies": {"@bika.ai/bika-zapier": "workspace:*", "@bika/contents": "workspace:*", "@bika/domains": "workspace:*", "@bika/types": "workspace:*", "@bika/ui": "workspace:*", "@clerk/nextjs": "^6.5.0", "@toolsdk.ai/domain": "workspace:*", "@toolsdk.ai/mcp-server": "workspace:*", "@toolsdk.ai/orm": "workspace:*", "@toolsdk.ai/plugin-core": "workspace:*", "@toolsdk.ai/registry": "1.0.90", "@toolsdk.ai/sdk-ts": "workspace:*", "axios": "^1.9.0", "basenext": "workspace:*", "live-plugin-manager": "^1.0.0", "next": "15.3.3", "python-shell": "^5.0.0", "random-words": "^2.0.1", "react": "18.3.1", "react-dom": "18.3.1", "sharelib": "workspace:*", "zod": "^3.23.30"}, "devDependencies": {"@types/node": "^20.11.24", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^9", "eslint-config-next": "15.3.3", "msw": "^2.7.0", "postcss": "^8", "tailwindcss": "^3.4.1", "tsx": "^4.19.2", "typescript": "^5.7.2", "vitest": "^3.2.4"}, "scripts": {"lint": "eslint", "test": "dotenv -e ../../../apps/toolsdk.ai/.env.local -- vitest"}, "keywords": [], "author": "", "license": "ISC"}