'use client';

import { LinearProgress } from '@mui/joy';
import FormHelperText from '@mui/joy/FormHelperText';
import { saveAs } from 'file-saver';
import type React from 'react';
import { useEffect, useMemo, useRef, useState } from 'react';
import type { ILocaleContext } from '@bika/contents/i18n';
import type { FileBO, AttachmentFileBOData } from '@bika/types/document/bo';
import type { INodeResourceApi } from '@bika/types/node/context';
import { FormLabel } from '../../form-components';
import { directDownload } from '../../preview-attachment/utils/download';
import { FileItem } from '../components/file-item';
import { getAttachmentDisplayPath } from '../utils/get-attachment-display-path';

// 提取接口定义
interface IFileUploadAndDisplayBOInputProps {
  locale: ILocaleContext;
  api: INodeResourceApi;
  required?: boolean;
  label?: string;
  setErrors?: (errors?: Record<string, string>) => void;
  value: FileBO;
  setValue: (value: FileBO) => void;
}

// 提取常量
const MAX_FILE_SIZE = 500 * 1024 * 1024; // 500MB
const UPLOAD_INTERVAL = 1000; // 1秒

// 提取上传进度模拟逻辑
const useUploadProgress = () => {
  const [progress, setProgress] = useState<number | null>(null);

  const startFakeProgress = () => {
    const timer = setInterval(() => {
      setProgress((prev) => {
        if (prev === null) return 10;
        if (prev >= 80) {
          clearInterval(timer);
          return 80;
        }
        const increment = Math.floor(Math.random() * 20) + 1;
        return Math.min(prev + increment, 80);
      });
    }, UPLOAD_INTERVAL);
    return timer;
  };

  const resetProgress = () => setProgress(null);

  return { progress, startFakeProgress, resetProgress };
};

// 主组件
export const FileUploadAndDisplayBOInput = (props: IFileUploadAndDisplayBOInputProps) => {
  const { t } = props.locale;
  const inputRef = useRef<HTMLInputElement | null>(null);
  const [isDropOver, setIsDropOver] = useState(false);
  const [localFile, setLocalFile] = useState<File | undefined>(undefined);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const { progress, startFakeProgress, resetProgress } = useUploadProgress();

  // 处理必填校验
  useEffect(() => {
    if (!props.required || !props.setErrors) return;
    const error = props.value.file ? '' : t.global.action.cannot_be_empty;
    props.setErrors({ fileInput: error });
    setUploadError(error);

    return () => {
      if (props.required && props.setErrors) {
        props.setErrors({ fileInput: '' });
      }
    };
  }, [JSON.stringify(props.value.file), props.required]);

  const fileNodeData: AttachmentFileBOData | undefined = useMemo(() => {
    if (!props.value.file) return undefined;
    return props.value.file.fileType === 'ATTACHMENT' ? props.value.file : undefined;
  }, [props.value.file]);

  // 文件上传处理
  const handleUpload = async (file: File) => {
    if (file.size > MAX_FILE_SIZE) {
      setUploadError('文件大小不能超过 500MB');
      return;
    }

    setUploadError(null);
    setLocalFile(file);
    const timer = startFakeProgress();

    try {
      const res = await props.api.node.uploadFile({ file, filePrefix: 'node' });
      if (res) {
        props.setValue({
          ...props.value,
          file: {
            fileType: 'ATTACHMENT',
            attachmentId: res.id,
            attachment: res,
          },
        });
      }
    } catch (error) {
      setUploadError('上传失败');
    } finally {
      clearInterval(timer);
      resetProgress();
    }
  };

  // 事件处理
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    handleUpload(file);
    e.target.value = '';
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDropOver(false);
    handleUpload(e.dataTransfer.files[0]);
  };

  const handleDelete = () => {
    props.setValue({
      ...props.value,
      file: undefined,
    });
    setLocalFile(undefined);
  };

  const handleRetry = () => {
    if (!localFile) return;
    const dataTransfer = new DataTransfer();
    dataTransfer.items.add(localFile);
    handleUpload(dataTransfer.files[0]);
  };

  const downloadFile = async () => {
    if (!fileNodeData) return;
    if (localFile) {
      saveAs(localFile);
    } else {
      if (!fileNodeData.downloadUrl) return;
      await directDownload(fileNodeData.downloadUrl);
    }
  };

  return (
    <>
      <FormLabel required={props.required}>{props.label}</FormLabel>

      {fileNodeData || localFile ? (
        <div>
          <FileItem
            url={getAttachmentDisplayPath(fileNodeData, localFile)}
            deleteFile={handleDelete}
            uploadProgress={progress}
            retryUpload={handleRetry}
            isError={localFile ? Boolean(uploadError) : null}
            downloadFile={downloadFile}
            name={localFile ? localFile.name : fileNodeData?.attachment.name ?? ''}
          />
          {progress !== null && (
            <div className="mt-1">
              <LinearProgress determinate value={progress} />
            </div>
          )}
          {uploadError && <FormHelperText sx={{ color: 'var(--status-danger)' }}>{uploadError}</FormHelperText>}
        </div>
      ) : (
        <div>
          <input type="file" ref={inputRef} onChange={handleChange} hidden defaultValue="" />
          <div
            className="border-[1px] border-dashed w-full h-[144px] flex flex-col justify-center items-center space-x-1 rounded focus:border-[--brand] text-[--text-secondary] outline-none px-1"
            style={{
              borderColor: isDropOver ? 'var(--brand)' : 'var(--border-default)',
            }}
            onDrop={handleDrop}
            onDragOver={(e) => {
              e.preventDefault();
              setIsDropOver(true);
            }}
            onDragLeave={() => setIsDropOver(false)}
          >
            <div className="flex items-center justify-center mb-2">
              <p onClick={() => inputRef.current?.click()} className="text-[--brand] cursor-pointer">
                {t.record.add_local_file}
              </p>
              &nbsp;{t.auth.or}
              <p>{t.record.paste_or_drop_file_upload}</p>
            </div>
            <div>{t.record.max_file_size}</div>
          </div>
          {uploadError && <FormHelperText sx={{ color: 'var(--status-danger)' }}>{uploadError}</FormHelperText>}
        </div>
      )}
    </>
  );
};
