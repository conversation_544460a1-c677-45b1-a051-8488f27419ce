export async function directDownload(href: string, name?: string) {
  const a = document.createElement('a');
  const formatHref = href.startsWith('http') ? href : `${window.location.origin}${href}`;
  try {
    const response = await fetch(formatHref);
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);

    a.download = name || 'Unknown';
    a.href = url;
    // a.target = '_blank'; // 对于跨域资源，在新窗口打开
    document.body.appendChild(a);
    a.click();
    a.remove();
  } catch {
    const url = new URL(formatHref);
    if (name) {
      a.download = name;
    }
    a.href = url.href;
    document.body.appendChild(a);
    a.click();
    a.remove();
  }
}
