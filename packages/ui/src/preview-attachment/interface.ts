import { AttachmentCellData } from '@bika/types/database/bo';
import { AttachmentCellValue } from '@bika/types/database/vo';

export type ITranslatePosition = { x: number; y: number };

export interface ITransFormInfo {
  rotate: number;
  scale: number;
  translatePosition: ITranslatePosition;
  initActualScale: number;
}

export interface IAttachmentPreviewProps {
  file: AttachmentVOExtends;
}

export interface IAttachmentItem {
  file: File;
  id: string;
  status: UploadStatus;
  progress?: number;
}

export type IAttachments = IAttachmentItem[];

export enum UploadStatus {
  PENDING,
  SUCCESS,
  FAIL,
  UPLOADING,
}

export interface AttachmentVOExtends extends AttachmentCellData {
  links?: AttachmentCellValue;
  originFile?: File;
}

// 附件字段单元格值合并, 替换上面的 AttachmentVOExtends
export type MergedAttachmentCellValue = AttachmentCellData &
  AttachmentCellValue & {
    originFile?: File;
  };
// TODO 重构 Merge AttachmentVOExtends | ISimpleAttachment

export interface ISimpleAttachment {
  name?: string;
  contentType?: string;
  url: string;
  variant: 'kkfile';
}
