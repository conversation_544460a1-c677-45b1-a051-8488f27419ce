import { z } from 'zod';
import { BaseResourceNodeSchema } from './bo-trigger-output';
import { WidgetVOSchema } from '../dashboard/vo-dashboard';
import { RecordVOSchema } from '../database/vo-record';
import { MemberVOSchema } from '../unit/vo-member';

export const BaseActionOutputSchema = z.object({});

export const AIModelActionOutputSchema = BaseActionOutputSchema.extend({
  text: z.string(),
});
export type AIModelActionOutput = z.infer<typeof AIModelActionOutputSchema>;

export const AISummaryActionOutputSchema = BaseActionOutputSchema.extend({
  message: z.string(),
});
export type AISummaryActionOutput = z.infer<typeof AISummaryActionOutputSchema>;

export const DelayActionOutputSchema = BaseActionOutputSchema.extend({
  endTime: z.string(),
});
export type DelayActionOutput = z.infer<typeof DelayActionOutputSchema>;

export const FindMembersActionOutputSchema = BaseActionOutputSchema.extend({
  members: z.array(MemberVOSchema),
});
export type FindMembersActionOutput = z.infer<typeof FindMembersActionOutputSchema>;

const MissionInActionOutputSchema = z.object({
  id: z.string(),
  name: z.string(),
  url: z.string(),
  dueAt: z.string().datetime().optional(),
});
const FindMissionsActionOutputSchema = BaseActionOutputSchema.extend({
  missions: z.array(MissionInActionOutputSchema),
});
export type FindMissionsActionOutput = z.infer<typeof FindMissionsActionOutputSchema>;
export const CreateMissionActionOutputSchema = FindMissionsActionOutputSchema;
export type CreateMissionActionOutput = z.infer<typeof CreateMissionActionOutputSchema>;

export const FindRecordsActionOutputSchema = BaseActionOutputSchema.extend({
  record: RecordVOSchema.optional(),
  records: z.array(RecordVOSchema),
});
export type FindRecordsActionOutput = z.infer<typeof FindRecordsActionOutputSchema>;
export const CreateRecordActionOutputSchema = BaseActionOutputSchema.extend({
  record: RecordVOSchema,
});
export type CreateRecordActionOutput = z.infer<typeof CreateRecordActionOutputSchema>;
export const UpdateRecordsActionOutputSchema = BaseActionOutputSchema.extend({
  // keep the original 'record' field to maintain backward compatibility with existing variable references, only has value when records.length === 1
  record: RecordVOSchema.optional(),
  records: z.array(RecordVOSchema),
});
export type UpdateRecordsActionOutput = z.infer<typeof UpdateRecordsActionOutputSchema>;
export const RecordTypeActionOutputSchema = z.union([
  FindRecordsActionOutputSchema,
  CreateRecordActionOutputSchema,
  UpdateRecordsActionOutputSchema,
]);

export const FindDashboardActionOutputSchema = BaseActionOutputSchema.extend({
  dashboard: BaseResourceNodeSchema,
});
export type FindDashboardActionOutput = z.infer<typeof FindDashboardActionOutputSchema>;

export const FindWidgetActionOutputSchema = BaseActionOutputSchema.extend({
  widget: WidgetVOSchema,
});
export type FindWidgetActionOutput = z.infer<typeof FindWidgetActionOutputSchema>;

const SendEmailActionOutputSchema = BaseActionOutputSchema.extend({
  messageId: z.string().optional(),
  emails: z.array(z.string()).optional(),
  cc: z.array(z.string()).optional(),
  bcc: z.array(z.string()).optional(),
  replyTo: z.array(z.string()).optional(),
});
export type SendEmailActionOutput = z.infer<typeof SendEmailActionOutputSchema>;

const SendReportActionOutputSchema = BaseActionOutputSchema.extend({
  reports: z
    .object({
      id: z.string(),
      subject: z.string(),
      url: z.string(),
    })
    .array(),
});
export type SendReportActionOutput = z.infer<typeof SendReportActionOutputSchema>;

const FormAppAIActionOutputSchema = BaseActionOutputSchema.or(z.unknown());
export type FormAppAIActionOutput = z.infer<typeof FormAppAIActionOutputSchema>;

const ToolSDKAIActionOutputSchema = BaseActionOutputSchema.or(z.unknown());
export type ToolSDKAIActionOutput = z.infer<typeof ToolSDKAIActionOutputSchema>;

const RunScriptActionOutputSchema = BaseActionOutputSchema.or(z.unknown());
export type RunScriptActionOutput = z.infer<typeof RunScriptActionOutputSchema>;

const TwitterCreateTweetOutputSchema = BaseActionOutputSchema.extend({
  id: z.string(),
  text: z.string(),
  logs: z.array(z.string()),
});
export type TwitterCreateTweetOutput = z.infer<typeof TwitterCreateTweetOutputSchema>;

const TwitterUploadMediaOutputSchema = BaseActionOutputSchema.extend({
  mediaIds: z.array(z.string()),
  logs: z.array(z.string()),
});
export type TwitterUploadMediaOutput = z.infer<typeof TwitterUploadMediaOutputSchema>;

export const WebhookActionOutputSchema = BaseActionOutputSchema.extend({
  status: z.number(),
  body: z.unknown(),
});
export type WebhookActionOutput = z.infer<typeof WebhookActionOutputSchema>;

export const WebhookActionOutputError = (message: string) => ({
  status: 400,
  body: {
    message,
  },
});

export const ActionOutputSchema = z
  .union([
    BaseActionOutputSchema,
    AIModelActionOutputSchema,
    AISummaryActionOutputSchema,
    DelayActionOutputSchema,
    FindMembersActionOutputSchema,
    FindMissionsActionOutputSchema,
    CreateMissionActionOutputSchema,
    FindRecordsActionOutputSchema,
    CreateRecordActionOutputSchema,
    UpdateRecordsActionOutputSchema,
    FindDashboardActionOutputSchema,
    FindWidgetActionOutputSchema,
    SendEmailActionOutputSchema,
    SendReportActionOutputSchema,
    FormAppAIActionOutputSchema,
    RunScriptActionOutputSchema,
    WebhookActionOutputSchema,
    ToolSDKAIActionOutputSchema,
    TwitterUploadMediaOutputSchema,
    TwitterCreateTweetOutputSchema,
  ])
  .or(z.object({ error: z.string() }));
export type ActionOutput = z.infer<typeof ActionOutputSchema>;
