import { z } from 'zod';
import { DatabaseRecordSchema } from './bo-database';
import { FieldVOSchema } from './vo-field';
import { ViewDetailVOSchema, ViewVOSchema } from './vo-view';

export const BaseDatabaseVOSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  spaceId: z.string(),
});

export type BaseDatabaseVO = z.infer<typeof BaseDatabaseVOSchema>;

export const DatabaseVOSchema = BaseDatabaseVOSchema.extend({
  views: z.array(ViewVOSchema),
});
export type DatabaseVO = z.infer<typeof DatabaseVOSchema>;

export const DocVOSchema = z.object({
  id: z.string(),
  name: z.string(),
  markdown: z.string(),
});
export type DocVO = z.infer<typeof DocVOSchema>;

export const DatabaseDetailVOSchema = DatabaseVOSchema.extend({
  views: z.array(ViewDetailVOSchema),
});
export type DatabaseDetailVO = z.infer<typeof DatabaseDetailVOSchema>;

export const DatabaseImportExcelPreviewVOSchema = z.object({
  fields: z.array(FieldVOSchema),
  records: z.array(DatabaseRecordSchema),
  total: z.number(),
});
export type DatabaseImportExcelPreviewVO = z.infer<typeof DatabaseImportExcelPreviewVOSchema>;
