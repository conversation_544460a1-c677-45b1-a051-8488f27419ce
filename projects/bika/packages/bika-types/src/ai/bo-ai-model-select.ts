import { z } from 'zod';
import { PresetLanguageAIModelDefSchema } from './bo-ai-model';
import { CustomAIProviderIntegrationSchema } from '../integration/bo-ai-integrations';

///

export const AIModelCustomManualConfigSchema = z.object({
  type: z.literal('manual'),
  provider: CustomAIProviderIntegrationSchema.optional(),
  modelId: z.string().optional(),
});
export type IAIModelCustomManualConfig = z.infer<typeof AIModelCustomManualConfigSchema>;

export const AIModelCustomConfigBOSchema = z.object({
  kind: z.literal('custom'),
  custom: z.union([
    z.object({
      type: z.literal('integration'),
      integrationId: z.string().optional(),
      modelId: z.string().optional(),
    }),
    AIModelCustomManualConfigSchema,
  ]),
});
export type IAIModelCustomSelectBO = z.infer<typeof AIModelCustomConfigBOSchema>;

export const AIModelSelectBOSchema = z.union([
  z.object({
    kind: z.literal('auto'),
  }),
  z.object({
    kind: z.literal('preset'),
    model: PresetLanguageAIModelDefSchema, // Replace with actual AIModelDef schema
  }),
  AIModelCustomConfigBOSchema,
]);
export type IAIModelSelectBO = z.infer<typeof AIModelSelectBOSchema>;
