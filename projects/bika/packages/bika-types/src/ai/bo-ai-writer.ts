import { z } from 'zod';
import { AutomationSchema, BaseTriggerSchema, BaseActionSchema } from '../automation/bo';
import {
  DatabaseSingleTextFieldSchema,
  DatabaseLongTextFieldSchema,
  DatabaseCheckBoxFieldSchema,
  DatabaseCurrencyFieldSchema,
  DatabaseDateTimeFieldSchema,
  DatabaseNumberFieldSchema,
  DatabasePercentFieldSchema,
  DatabasePhoneFieldSchema,
  DatabaseEmailFieldSchema,
  DatabaseRatingFieldSchema,
  DatabaseSingleSelectFieldSchema,
  DatabaseMultiSelectFieldSchema,
  DatabaseURLFieldSchema,
  DatabaseAITextFieldSchema,
  RecordDataSchema,
} from '../database/bo';
import { FolderSchema, BaseNodeResourceBOSchema } from '../node/bo';
import { LocaleSchema } from '../system';
import { AIUsageSchema } from './bo-ai';
import { AIModelSelectBOSchema } from './bo-ai-model-select';

// Custom text generation
export const AIWriteTextGeneration = z.object({
  type: z.literal('TEXT_GENERATION'),
  // Creator prompt, used by template builders to configure prompts
  creatorPrompt: z.string(),
  // Additional context, can be anything
  extra: z.string().optional(),
});

export const AIWriteTextRephrase = z.object({
  type: z.literal('TEXT_REPHRASE'),
  creatorPrompt: z.string(),
  rephraseText: z.string(),
});

export const AIWriterTranslate = z.object({
  type: z.literal('TRANSLATE'),
  // what lang return
  lang: z.string().default('en').optional(),
});

export const AIWriterI18NString = z.object({
  type: z.literal('I18N_STRING'),
  // what lang return
  langs: z.array(LocaleSchema).optional(),
});

const NodeResourceDTOSchema = BaseNodeResourceBOSchema.pick({
  id: true,
  resourceType: true,
  name: true,
  description: true,
});
export type NodeResourceDTO = z.infer<typeof NodeResourceDTOSchema>;

const FolderDTOSchema = NodeResourceDTOSchema.extend({
  resourceType: z.literal('FOLDER'),
  children: z.array(NodeResourceDTOSchema),
});
export type FolderDTO = z.infer<typeof FolderDTOSchema>;

// const DatabaseFieldDTOSchema = BaseDatabaseFieldSchema.pick({
//   name: true,
//   type: true,
//   description: true,
//   property: true,
// }).passthrough();

export const DatabaseFieldDTOSchema = z.discriminatedUnion('type', [
  DatabaseSingleTextFieldSchema.pick({
    name: true,
    type: true,
    description: true,
    property: true,
  }).partial({ property: true }),
  DatabaseLongTextFieldSchema.pick({
    name: true,
    type: true,
    description: true,
    property: true,
  }).partial({ property: true }),
  DatabaseCheckBoxFieldSchema.pick({
    name: true,
    type: true,
    description: true,
    property: true,
  }).partial({ property: true }),
  DatabaseCurrencyFieldSchema.pick({
    name: true,
    type: true,
    description: true,
    property: true,
  }).partial({ property: true }),
  DatabaseDateTimeFieldSchema.pick({
    name: true,
    type: true,
    description: true,
    property: true,
  }).partial({ property: true }),
  DatabaseNumberFieldSchema.pick({
    name: true,
    type: true,
    description: true,
    property: true,
  }).partial({ property: true }),
  DatabasePercentFieldSchema.pick({
    name: true,
    type: true,
    description: true,
    property: true,
  }).partial({ property: true }),
  DatabasePhoneFieldSchema.pick({
    name: true,
    type: true,
    description: true,
    property: true,
  }).partial({ property: true }),
  DatabaseEmailFieldSchema.pick({
    name: true,
    type: true,
    description: true,
    property: true,
  }).partial({ property: true }),
  DatabaseRatingFieldSchema.pick({
    name: true,
    type: true,
    description: true,
    property: true,
  }).partial({ property: true }),
  DatabaseSingleSelectFieldSchema.pick({
    name: true,
    type: true,
    description: true,
    property: true,
  }).partial({ property: true }),
  DatabaseMultiSelectFieldSchema.pick({
    name: true,
    type: true,
    description: true,
    property: true,
  }).partial({ property: true }),
  DatabaseURLFieldSchema.pick({
    name: true,
    type: true,
    description: true,
    property: true,
  }).partial({ property: true }),
]);

export type DatabaseFieldDTO = z.infer<typeof DatabaseFieldDTOSchema>;

const DatabaseDTOSchema = NodeResourceDTOSchema.extend({
  resourceType: z.literal('DATABASE'),
  fields: z.array(DatabaseFieldDTOSchema),
});
export type DatabaseDTO = z.infer<typeof DatabaseDTOSchema>;

const AutomationTriggerDTOSchema = BaseTriggerSchema.pick({
  triggerType: true,
  description: true,
});
export type AutomationTriggerDTO = z.infer<typeof AutomationTriggerDTOSchema>;

// export const ExtractedActionInputSchema = ActionSchema.transform((data) => data.input);
const AutomationActionDTOSchema = BaseActionSchema.pick({
  actionType: true,
  description: true,
});
export type AutomationActionDTO = z.infer<typeof AutomationActionDTOSchema>;

const AutomationDTOSchema = AutomationSchema.extend({
  resourceType: z.literal('AUTOMATION'),
  triggers: z.array(AutomationTriggerDTOSchema),
  actions: z.array(AutomationActionDTOSchema),
});
export type AutomationDTO = z.infer<typeof AutomationDTOSchema>;

export const AIWriterResourceDescription = z.object({
  type: z.literal('RESOURCE_DESCRIPTION'),
  resource: z.union([FolderDTOSchema, DatabaseDTOSchema, AutomationDTOSchema, NodeResourceDTOSchema]),
});

export const AIWriterResourceReadme = z.object({
  type: z.literal('RESOURCE_README'),
  // Folder BO with children is preferred
  folder: FolderSchema,
});

export const AIWriterRecordBase = z.object({
  fields: z.array(DatabaseFieldDTOSchema),
  databaseName: z.string().optional(),
  databaseDescription: z.string().optional(),
});

export const AIWriterRecordDetailFilling = AIWriterRecordBase.extend({
  type: z.literal('EXTRACT_TO_RECORD'),
});

export const AIWriterGenerateMockRecord = AIWriterRecordBase.extend({
  type: z.literal('GENERATE_MOCK_RECORD'),
});

export const AIWriterRecordCellFillingSchema = z.object({
  type: z.literal('RECORD_CELL_FILLING'),
  // If recordId exists, only the specified fields of the specified record are populated, otherwise the first record is used for preview
  recordId: z.string().optional(),
  // If record exists, only the specified fields of the specified record are populated, otherwise use the recordId
  cells: RecordDataSchema.optional(),
  databaseId: z.string(),
  // specify the field to fill, use for preview
  field: DatabaseAITextFieldSchema.optional(),
  // specify the fieldId to fill
  fieldId: z.string().optional(),
});

export type AIWriterRecordCellFilling = z.infer<typeof AIWriterRecordCellFillingSchema>;

export const AIWriterAIModelActionSchema = z.object({
  type: z.literal('AI_MODEL_ACTION'),
  automationId: z.string().optional(),
  actionId: z.string().optional(),
  model: AIModelSelectBOSchema,
});
export type AIWriterAIModelAction = z.infer<typeof AIWriterAIModelActionSchema>;

export const AIWriterSchema = z.discriminatedUnion('type', [
  AIWriterAIModelActionSchema,
  AIWriteTextGeneration,
  AIWriteTextRephrase,
  AIWriterTranslate,
  AIWriterI18NString,
  AIWriterRecordDetailFilling,
  AIWriterResourceDescription,
  AIWriterResourceReadme,
  AIWriterGenerateMockRecord,
  AIWriterRecordCellFillingSchema,
]);

export type AIWriter = z.infer<typeof AIWriterSchema>;
export type AIWriterType = AIWriter['type'];

export const AIWriterResponseSchema = z.object({
  data: z.any(),
  value: z.string().optional(),
  success: z.boolean().optional(),
  message: z.string().optional(),
  usages: z.array(AIUsageSchema).optional(),
});

export type AIWriterResponse = z.infer<typeof AIWriterResponseSchema>;
