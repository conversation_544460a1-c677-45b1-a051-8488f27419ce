import { SpacePlanType } from '@bika/types/pricing/bo';
import { getFeatureConfigMap } from './feature';
import { SpacePlanDescValue } from './types';

export const SpacePlanDescConfig: { [_plan in SpacePlanType]: SpacePlanDescValue } = {
  FREE: {
    planName: {
      en: 'Free',
      'zh-CN': '免费版',
      'zh-TW': '免費版',
      ja: '無料版',
    },
    description: {
      en: "Individuals' AI CRM and database",
      'zh-CN': '个人的AI数据库和知识库',
      'zh-TW': '個人的AI數據庫和知識庫',
      ja: '個人のAI CRMとデータベース',
    },
    // 'For individuals to organize and automate their work',
    highlights: [
      // "For individual and small team's AI CRM",
      {
        en: `3,000 daily AI credits`,
        'zh-CN': `每日 3,000 AI积分`,
        'zh-TW': `每日 3,000 AI積分`,
        ja: `1日あたり3,000 AIクレジット`,
      },
      {
        en: `${getFeatureConfigMap().FREE.storage}GB of storage`,
        'zh-CN': `${getFeatureConfigMap().FREE.storage}GB存储空间`,
        'zh-TW': `${getFeatureConfigMap().FREE.storage}GB存儲空間`,
        ja: `${getFeatureConfigMap().FREE.storage}GBのストレージ`,
      },
      {
        en: `${getFeatureConfigMap().FREE.recordsPerDatabase.toLocaleString()} records per database`,
        'zh-CN': `单表 ${getFeatureConfigMap().FREE.recordsPerDatabase.toLocaleString()} 行数据`,
        'zh-TW': `單表 ${getFeatureConfigMap().FREE.recordsPerDatabase.toLocaleString()} 行數據`,
        ja: `1つのデータベースに ${getFeatureConfigMap().FREE.recordsPerDatabase.toLocaleString()} 行のデータ`,
      },
      {
        en: `${getFeatureConfigMap().FREE.automationRuns.toLocaleString()} automation runs`,
        'zh-CN': `${getFeatureConfigMap().FREE.automationRuns.toLocaleString()} 次自动化执行`,
        'zh-TW': `${getFeatureConfigMap().FREE.automationRuns.toLocaleString()} 次自動化執行`,
        ja: `${getFeatureConfigMap().FREE.automationRuns.toLocaleString()} 回の自動化実行`,
      },
      {
        en: 'Missions, Reports, AI Summary, and more',
        'zh-CN': '智能任务、智能报告、AI助理等',
        'zh-TW': '智能任務、智能報告、AI助理等',
        ja: 'ミッション、レポート、AIサマリーなど',
      },
      {
        en: 'Free Send Bulk 50 SMS / 5 Email',
        'zh-CN': '免费发送 50 短信/ 5 邮件',
        'zh-TW': '免費發送 50 短信/ 5 郵件',
        ja: '無料で50のSMS/5以上のメールを送信',
      },
      {
        en: 'Up to 5 seats',
        'zh-CN': '最多5个成员',
        'zh-TW': '最多5個成員',
        ja: '最大5人',
      },
    ],
    includeTitle: {
      en: 'Forever FREE',
      'zh-CN': '永久免费',
      'zh-TW': '永久免費',
      ja: '永遠無料',
    },
  },
  STARTER: {
    planName: 'Starter',
    description: {
      en: "Professionals' intelligent sales, marketing and project hub",
      'zh-CN': '专业人士的智能销售、营销和项目中心',
      'zh-TW': '專業人士的智能銷售、營銷和項目中心',
      ja: 'プロフェッショナルのインテリジェントセールス、マーケティング、プロジェクトハブ',
    },
    includeTitle: {
      en: 'Everything in Free +',
      'zh-CN': '包含免费版所有功能，再加上',
      'zh-TW': '包含免費版所有功能，再加上',
      ja: '無料版のすべての機能を含む',
    },
    highlights: [
      // "For professional's AI CRM & marketing hub",
      {
        en: `${Math.floor(getFeatureConfigMap().PLUS.creditsPerSeat * 0.5).toLocaleString()} AI Credits per seat`,
        'zh-CN': `${Math.floor(getFeatureConfigMap().PLUS.creditsPerSeat * 0.5).toLocaleString()} AI积分/人`,
        'zh-TW': `${Math.floor(getFeatureConfigMap().PLUS.creditsPerSeat * 0.5).toLocaleString()} AI積分/人`,
        ja: `${Math.floor(getFeatureConfigMap().PLUS.creditsPerSeat * 0.5).toLocaleString()} AIクレジット/ユーザー`,
      },
      {
        en: `🎁 ${Math.floor(getFeatureConfigMap().PLUS.creditsPerSeat * 0.5).toLocaleString()} Extra credits limited offer`,
        'zh-CN': `🎁 ${Math.floor(getFeatureConfigMap().PLUS.creditsPerSeat * 0.5).toLocaleString()} 额外积分限时赠送`,
        'zh-TW': `🎁 ${Math.floor(getFeatureConfigMap().PLUS.creditsPerSeat * 0.5).toLocaleString()} 額外積分限時贈送`,
        ja: `🎁 ${Math.floor(getFeatureConfigMap().PLUS.creditsPerSeat * 0.5).toLocaleString()} 追加クレジット期間限定オファー`,
      },
      {
        en: `${getFeatureConfigMap().PLUS.storage}GB of storage`,
        'zh-CN': `${getFeatureConfigMap().PLUS.storage}GB存储空间`,
        'zh-TW': `${getFeatureConfigMap().PLUS.storage}GB存儲空間`,
        ja: `${getFeatureConfigMap().PLUS.storage}GBのストレージ`,
      },
      {
        en: `${getFeatureConfigMap().PLUS.recordsPerDatabase.toLocaleString()} records per database`,
        'zh-CN': `单表 ${getFeatureConfigMap().PLUS.recordsPerDatabase.toLocaleString()} 行数据`,
        'zh-TW': `單表 ${getFeatureConfigMap().PLUS.recordsPerDatabase.toLocaleString()} 行數據`,
        ja: `1つのデータベースに ${getFeatureConfigMap().PLUS.recordsPerDatabase.toLocaleString()} 行のデータ`,
      },
      {
        en: `${getFeatureConfigMap().PLUS.automationRuns.toLocaleString()} automation runs`,
        'zh-CN': `${getFeatureConfigMap().PLUS.automationRuns.toLocaleString()} 次自动化执行`,
        'zh-TW': `${getFeatureConfigMap().PLUS.automationRuns.toLocaleString()} 次自動化執行`,
        ja: `${getFeatureConfigMap().PLUS.automationRuns.toLocaleString()} 回の自動化実行`,
      },
      {
        en: 'Public & Share',
        'zh-CN': '公开分享',
        'zh-TW': '公開分享',
        ja: '公開＆共有',
      },
      {
        en: 'More database fields, automation actions',
        'zh-CN': '更多数据库字段、自动化操作',
        'zh-TW': '更多數據庫字段、自動化操作',
        ja: 'より多くのデータベースフィールド、自動化アクション',
      },
      {
        en: 'More free SMS, Email, Reports, Missions, AI',
        'zh-CN': '更多免费短信、邮件、报告、任务、AI',
        'zh-TW': '更多免費短信、郵件、報告、任務、AI',
        ja: 'より多くの無料SMS、メール、レポート、ミッション、AI',
      },
    ],
  },
  PLUS: {
    planName: {
      en: 'Plus',
      'zh-CN': 'Plus版',
      'zh-TW': 'Plus版',
      ja: 'Plus',
    },
    description: {
      en: "Professionals' intelligent sales, marketing and project hub",
      'zh-CN': '专业人士的智能销售、营销和项目中心',
      'zh-TW': '專業人士的智能銷售、營銷和項目中心',
      ja: 'プロフェッショナルのインテリジェントセールス、マーケティング、プロジェクトハブ',
    },
    includeTitle: {
      en: 'Everything in Free +',
      'zh-CN': '包含免费版所有功能，再加上',
      'zh-TW': '包含免費版所有功能，再加上',
      ja: '無料版のすべての機能を含む',
    },
    highlights: [
      // "For professional's AI CRM & marketing hub",
      {
        en: `${Math.floor(getFeatureConfigMap().PLUS.creditsPerSeat * 0.5).toLocaleString()} AI Credits per seat`,
        'zh-CN': `${Math.floor(getFeatureConfigMap().PLUS.creditsPerSeat * 0.5).toLocaleString()} AI积分/人`,
        'zh-TW': `${Math.floor(getFeatureConfigMap().PLUS.creditsPerSeat * 0.5).toLocaleString()} AI積分/人`,
        ja: `${Math.floor(getFeatureConfigMap().PLUS.creditsPerSeat * 0.5).toLocaleString()} AIクレジット/ユーザー`,
      },
      {
        en: `${Math.floor(getFeatureConfigMap().PLUS.creditsPerSeat * 0.5).toLocaleString()} Extra credits limited offer`,
        'zh-CN': `${Math.floor(getFeatureConfigMap().PLUS.creditsPerSeat * 0.5).toLocaleString()} 额外积分限时赠送`,
        'zh-TW': `${Math.floor(getFeatureConfigMap().PLUS.creditsPerSeat * 0.5).toLocaleString()} 額外積分限時贈送`,
        ja: `${Math.floor(getFeatureConfigMap().PLUS.creditsPerSeat * 0.5).toLocaleString()} 追加クレジット期間限定オファー`,
      },
      {
        en: `${getFeatureConfigMap().PLUS.storage}GB of storage`,
        'zh-CN': `${getFeatureConfigMap().PLUS.storage}GB存储空间`,
        'zh-TW': `${getFeatureConfigMap().PLUS.storage}GB存儲空間`,
        ja: `${getFeatureConfigMap().PLUS.storage}GBのストレージ`,
      },
      {
        en: `${getFeatureConfigMap().PLUS.recordsPerDatabase.toLocaleString()} records per database`,
        'zh-CN': `单表 ${getFeatureConfigMap().PLUS.recordsPerDatabase.toLocaleString()} 行数据`,
        'zh-TW': `單表 ${getFeatureConfigMap().PLUS.recordsPerDatabase.toLocaleString()} 行數據`,
        ja: `1つのデータベースに ${getFeatureConfigMap().PLUS.recordsPerDatabase.toLocaleString()} 行のデータ`,
      },
      {
        en: `${getFeatureConfigMap().PLUS.automationRuns.toLocaleString()} automation runs`,
        'zh-CN': `${getFeatureConfigMap().PLUS.automationRuns.toLocaleString()} 次自动化执行`,
        'zh-TW': `${getFeatureConfigMap().PLUS.automationRuns.toLocaleString()} 次自動化執行`,
        ja: `${getFeatureConfigMap().PLUS.automationRuns.toLocaleString()} 回の自動化実行`,
      },
      {
        en: 'Public & Share',
        'zh-CN': '公开分享',
        'zh-TW': '公開分享',
        ja: '公開＆共有',
      },
      {
        en: 'More database fields, automation actions',
        'zh-CN': '更多数据库字段、自动化操作',
        'zh-TW': '更多數據庫字段、自動化操作',
        ja: 'より多くのデータベースフィールド、自動化アクション',
      },
      {
        en: 'More free SMS, Email, Reports, Missions, AI',
        'zh-CN': '更多免费短信、邮件、报告、任务、AI',
        'zh-TW': '更多免費短信、郵件、報告、任務、AI',
        ja: 'より多くの無料SMS、メール、レポート、ミッション、AI',
      },
    ],
  },
  PRO: {
    planName: 'Pro',
    description: 'Pro',
    includeTitle: 'Everything in Plus +',
    highlights: [
      "For professional's AI CRM & marketing hub",
      {
        en: `${Math.floor(getFeatureConfigMap().PRO.creditsPerSeat * 0.5).toLocaleString()} AI Credits per seat`,
        'zh-CN': `${Math.floor(getFeatureConfigMap().PRO.creditsPerSeat * 0.5).toLocaleString()} AI积分/人`,
        'zh-TW': `${Math.floor(getFeatureConfigMap().PRO.creditsPerSeat * 0.5).toLocaleString()} AI積分/人`,
        ja: `${Math.floor(getFeatureConfigMap().PRO.creditsPerSeat * 0.5).toLocaleString()} AIクレジット/ユーザー`,
      },
      {
        en: `🎁 ${Math.floor(getFeatureConfigMap().PRO.creditsPerSeat * 0.5).toLocaleString()} Extra credits limited offer`,
        'zh-CN': `🎁 ${Math.floor(getFeatureConfigMap().PRO.creditsPerSeat * 0.5).toLocaleString()} 额外积分限时赠送`,
        'zh-TW': `🎁 ${Math.floor(getFeatureConfigMap().PRO.creditsPerSeat * 0.5).toLocaleString()} 額外積分限時贈送`,
        ja: `🎁 ${Math.floor(getFeatureConfigMap().PRO.creditsPerSeat * 0.5).toLocaleString()} 追加クレジット期間限定オファー`,
      },
      `${getFeatureConfigMap().PRO.storage}GB of storage`,
      `${getFeatureConfigMap().PRO.recordsPerDatabase.toLocaleString()} records per database`,
      `${getFeatureConfigMap().PRO.automationRuns.toLocaleString()} automation runs`,
      'IM / Email Support',
      'Team / Organization / Permission',
      'Advanced OpenAPI and AI models',
    ],
  },
  TEAM: {
    planName: {
      en: 'Team',
      'zh-CN': '团队版',
      'zh-TW': '團隊版',
      ja: 'チーム版',
    },
    description: {
      en: `Teams' intelligence database and knowledge-base`,
      'zh-CN': '适用于团队的智能数据库和知识库',
      'zh-TW': '適用於團隊的智能數據庫和知識庫',
      ja: 'チーム向けのデータベースとナレッジベースをインテリジェントにする',
    },
    includeTitle: {
      en: 'Everything in Plus +',
      'zh-CN': '包含Plus版所有功能，再加上',
      'zh-TW': '包含Plus版所有功能，再加上',
      ja: 'プラスのすべての機能を含む',
    },
    highlights: [
      // "For teams's AI CRM & marketing hub",
      {
        en: `${Math.floor((getFeatureConfigMap().TEAM.creditsPerSeat * 0.5) / 1000).toLocaleString()}K AI Credits per seat`,
        'zh-CN': `${Math.floor((getFeatureConfigMap().TEAM.creditsPerSeat * 0.5) / 1000).toLocaleString()}K AI积分/人`,
        'zh-TW': `${Math.floor((getFeatureConfigMap().TEAM.creditsPerSeat * 0.5) / 1000).toLocaleString()}K AI積分/人`,
        ja: `${Math.floor((getFeatureConfigMap().TEAM.creditsPerSeat * 0.5) / 1000).toLocaleString()}K AIクレジット/ユーザー`,
      },
      {
        en: `${Math.floor((getFeatureConfigMap().TEAM.creditsPerSeat * 0.5) / 1000).toLocaleString()}K Extra credits limited offer`,
        'zh-CN': `${Math.floor((getFeatureConfigMap().TEAM.creditsPerSeat * 0.5) / 1000).toLocaleString()}K 额外积分限时赠送`,
        'zh-TW': `${Math.floor((getFeatureConfigMap().TEAM.creditsPerSeat * 0.5) / 1000).toLocaleString()}K 額外積分限時贈送`,
        ja: `${Math.floor((getFeatureConfigMap().TEAM.creditsPerSeat * 0.5) / 1000).toLocaleString()}K 追加クレジット期間限定オファー`,
      },
      {
        en: `${getFeatureConfigMap().TEAM.storage}GB of storage`,
        'zh-CN': `${getFeatureConfigMap().TEAM.storage}GB存储空间`,
        'zh-TW': `${getFeatureConfigMap().TEAM.storage}GB存儲空間`,
        ja: `${getFeatureConfigMap().TEAM.storage}GBのストレージ`,
      },
      {
        en: `${getFeatureConfigMap().TEAM.recordsPerDatabase.toLocaleString()} records per database`,
        'zh-CN': `单表 ${getFeatureConfigMap().TEAM.recordsPerDatabase.toLocaleString()} 行数据`,
        'zh-TW': `單表 ${getFeatureConfigMap().TEAM.recordsPerDatabase.toLocaleString()} 行數據`,
        ja: `1つのデータベースに ${getFeatureConfigMap().TEAM.recordsPerDatabase.toLocaleString()} 行のデータ`,
      },
      {
        en: `${getFeatureConfigMap().TEAM.automationRuns.toLocaleString()} automation runs`,
        'zh-CN': `${getFeatureConfigMap().TEAM.automationRuns.toLocaleString()} 次自动化执行`,
        'zh-TW': `${getFeatureConfigMap().TEAM.automationRuns.toLocaleString()} 次自動化執行`,
        ja: `${getFeatureConfigMap().TEAM.automationRuns.toLocaleString()} 回の自動化実行`,
      },
      {
        en: 'IM / Email Support',
        'zh-CN': 'IM / 邮件支持',
        'zh-TW': 'IM / 郵件支持',
        ja: 'IM / メールサポート',
      },
      {
        en: 'Team / Organization / Permission',
        'zh-CN': '团队 / 组织 / 权限',
        'zh-TW': '團隊 / 組織 / 權限',
        ja: 'チーム / 組織 / 権限',
      },
      {
        en: 'Advanced OpenAPI and AI models',
        'zh-CN': '高级OpenAPI和AI模型',
        'zh-TW': '高級OpenAPI和AI模型',
        ja: '高度なOpenAPIとAIモデル',
      },
    ],
  },
  BUSINESS: {
    planName: {
      en: 'Business',
      'zh-CN': '商业版',
      'zh-TW': '商業版',
      ja: 'ビジネス版',
    },
    description: {
      en: "Growing businesses' Enterprise AI with scalability and security",
      'zh-CN': '适用快速增长的企业和组织的企业级AI，以实现可扩展性、控制和安全性',
      'zh-TW': '適用於快速增長的企業和組織的企業級AI，以實現可擴展性、控制和安全性',
      ja: '急速に成長する企業や組織のエンタープライズAIを活用して、拡張性、コントロール、セキュリティを実現する',
    },
    includeTitle: {
      en: 'Everything in Team +',
      'zh-CN': '包含团队版所有功能，再加上',
      'zh-TW': '包含團隊版所有功能，再加上',
      ja: 'チームのすべての機能を含む',
    },
    highlights: [
      // "Businesses' AI Automation Database & Knowlede-base",
      {
        en: `${Math.floor((getFeatureConfigMap().BUSINESS.creditsPerSeat * 0.5) / 1000).toLocaleString()}K AI Credits per seat`,
        'zh-CN': `${Math.floor((getFeatureConfigMap().BUSINESS.creditsPerSeat * 0.5) / 1000).toLocaleString()}K AI积分/人`,
        'zh-TW': `${Math.floor((getFeatureConfigMap().BUSINESS.creditsPerSeat * 0.5) / 1000).toLocaleString()}K AI積分/人`,
        ja: `${Math.floor((getFeatureConfigMap().BUSINESS.creditsPerSeat * 0.5) / 1000).toLocaleString()}K AIクレジット/ユーザー`,
      },
      {
        en: `${Math.floor((getFeatureConfigMap().BUSINESS.creditsPerSeat * 0.5) / 1000).toLocaleString()}K Extra credits limited offer`,
        'zh-CN': `${Math.floor((getFeatureConfigMap().BUSINESS.creditsPerSeat * 0.5) / 1000).toLocaleString()}K 额外积分限时赠送`,
        'zh-TW': `${Math.floor((getFeatureConfigMap().BUSINESS.creditsPerSeat * 0.5) / 1000).toLocaleString()}K 額外積分限時贈送`,
        ja: `${Math.floor((getFeatureConfigMap().BUSINESS.creditsPerSeat * 0.5) / 1000).toLocaleString()}K 追加クレジット期間限定オファー`,
      },
      {
        en: `${getFeatureConfigMap().BUSINESS.storage}GB of storage`,
        'zh-CN': `${getFeatureConfigMap().BUSINESS.storage}GB存储空间`,
        'zh-TW': `${getFeatureConfigMap().BUSINESS.storage}GB存儲空間`,
        ja: `${getFeatureConfigMap().BUSINESS.storage}GBのストレージ`,
      },
      {
        en: `${getFeatureConfigMap().BUSINESS.recordsPerDatabase.toLocaleString()} records per database`,
        'zh-CN': `单表 ${getFeatureConfigMap().BUSINESS.recordsPerDatabase.toLocaleString()} 行数据`,
        'zh-TW': `單表 ${getFeatureConfigMap().BUSINESS.recordsPerDatabase.toLocaleString()} 行數據`,
        ja: `1つのデータベースに ${getFeatureConfigMap().BUSINESS.recordsPerDatabase.toLocaleString()} 行のデータ`,
      },
      {
        en: `${getFeatureConfigMap().BUSINESS.automationRuns.toLocaleString()} automation runs`,
        'zh-CN': `${getFeatureConfigMap().BUSINESS.automationRuns.toLocaleString()} 次自动化执行`,
        'zh-TW': `${getFeatureConfigMap().BUSINESS.automationRuns.toLocaleString()} 次自動化執行`,
        ja: `${getFeatureConfigMap().BUSINESS.automationRuns.toLocaleString()} 回の自動化実行`,
      },
      {
        en: 'Audit Logs',
        'zh-CN': '审计日志',
        'zh-TW': '審計日誌',
        ja: '監査ログ',
      },
      {
        en: 'Full integrations, automations',
        'zh-CN': '完整的集成、自动化',
        'zh-TW': '完整的集成、自動化',
        ja: '完全な統合、自動化',
      },
      {
        en: 'Advanced Permissions',
        'zh-CN': '高级权限',
        'zh-TW': '高級權限',
        ja: '高度な権限',
      },
    ],
  },
  ENTERPRISE: {
    planName: {
      en: 'Enterprise',
      'zh-CN': '企业版',
      'zh-TW': '企業版',
      ja: 'エンタープライズ版',
    },
    description: {
      en: 'Managed cloud edition with custom features for enterprises.',
      'zh-CN': '企业级云托管版，定制功能',
      'zh-TW': '企業級雲托管版，定制功能',
      ja: 'エンタープライズ向けのカスタム機能を備えたマネージドクラウドエディション',
    },
    includeTitle: {
      en: 'Everhting in Business +',
      'zh-CN': '包含商业版所有功能，再加上',
      'zh-TW': '包含商業版所有功能，再加上',
      ja: 'ビジネスのすべての機能を含む',
    },
    highlights: [
      {
        en: 'Custom features for enterprises',
        'zh-CN': '企业级定制功能',
        'zh-TW': '企業級定制功能',
        ja: '企業向けのカスタム機能',
      },
      {
        en: 'Custom permissions and identities management',
        'zh-CN': '自定义权限和身份管理',
        'zh-TW': '自定義權限和身份管理',
        ja: 'カスタム権限とアイデンティティ管理',
      },
      {
        en: 'Custom integrations and automations',
        'zh-CN': '自定义集成和自动化',
        'zh-TW': '自定義集成和自動化',
        ja: 'カスタム統合と自動化',
      },
    ],
  },
  ENTERPRISE_PRIVATE_CLOUD: {
    planName: {
      en: 'Dedicated cloud',
      'zh-CN': '专有云版',
      'zh-TW': '專有雲版',
      ja: '専用クラウド',
    },
    description: {
      en: 'Deploy on your private cloud, with DevOps managed by us',
      'zh-CN': '部署在您的私有云上，由我们的专业团队进行DevOps管理',
      'zh-TW': '部署在您的私有雲上，由我們的專業團隊進行DevOps管理',
      ja: 'プライベートクラウドにデプロイし、DevOpsを専門家が管理',
    },
    includeTitle: {
      en: 'Everything in Enterprise +',
      'zh-CN': '包含企业版所有功能，再加上',
      'zh-TW': '包含企業版所有功能，再加上',
      ja: 'エンタープライズのすべての機能を含む',
    },
    highlights: [
      {
        en: 'Self-hosted on your private cloud',
        'zh-CN': '部署在您的私有云上',
        'zh-TW': '部署在您的私有雲上',
        ja: 'プライベートクラウドにデプロイ',
      },
      {
        en: 'Full control and security',
        'zh-CN': '完全控制和安全性',
        'zh-TW': '完全控制和安全性',
        ja: '完全な制御とセキュリティ',
      },
      {
        en: 'DevOps managed by our professional team',
        'zh-CN': '由我们的专业团队进行DevOps管理',
        'zh-TW': '由我們的專業團隊進行DevOps管理',
        ja: '専門家によるDevOps管理',
      },
      {
        en: 'Keep version updates to the official cloud version',
        'zh-CN': '保持版本更新与官方云版同步',
        'zh-TW': '保持版本更新與官方雲版同步',
        ja: 'バージョンの更新を公式クラウドバージョンと同期',
      },
    ],
  },
  ENTERPRISE_SELF_HOSTED: {
    planName: {
      en: 'Self-hosted',
      'zh-CN': '私有化版',
      'zh-TW': '私有化版',
      ja: 'セルフホスティング版',
    },
    description: {
      en: 'Self-hosted by yourself, with the executable provided.',
      'zh-CN': '提供可执行程序包，您自行托管',
      'zh-TW': '提供可執行程序包，您自行托管',
      ja: '提供された実行可能ファイルで自己ホスト',
    },
    includeTitle: {
      en: 'Everything in Enterprise +',
      'zh-CN': '包含企业版所有功能，再加上',
      'zh-TW': '包含企業版所有功能，再加上',
      ja: 'エンタープライズのすべての機能を含む',
    },
    highlights: [
      {
        en: 'Self-hosted with the executable provided',
        'zh-CN': '提供可执行程序包，您自行托管',
        'zh-TW': '提供可執行程序包，您自行托管',
        ja: '提供された実行可能ファイルで自己ホスト',
      },
      {
        en: 'Full control and security',
        'zh-CN': '完全控制和安全性',
        'zh-TW': '完全控制和安全性',
        ja: '完全な制御とセキュリティ',
      },
      {
        en: 'Everything is under your control.',
        'zh-CN': '一切由您掌控',
        'zh-TW': '一切由您掌控',
        ja: 'すべてがあなたのコントロール下にあります',
      },
    ],
  },
  COMMUNITY: {
    planName: {
      en: 'Community',
      'zh-CN': '社区版',
      'zh-TW': '社區版',
      ja: 'コミュニティ版',
    },
    description: {
      en: 'FREE and self-hosted',
      'zh-CN': '自行托管，免费社区版',
      'zh-TW': '自行托管，免費社區版',
      ja: '無料で自己ホスト',
    },
    includeTitle: {
      en: 'Everything in Free +',
      'zh-CN': '包含免费版所有功能，再加上',
      'zh-TW': '包含免費版所有功能，再加上',
      ja: '無料版のすべての機能を含む',
    },
    highlights: [
      {
        en: 'For developers to taste fresh',
        'zh-CN': '为开发者提供最新体验',
        'zh-TW': '為開發者提供最新體驗',
        ja: '開発者が新鮮な味を楽しむため',
      },
      {
        en: 'Self-hosted with the executable provided',
        'zh-CN': '提供可执行程序包，您自行托管',
        'zh-TW': '提供可執行程序包，您自行托管',
        ja: '提供された実行可能ファイルで自己ホスト',
      },
    ],
  },
};
