// server ai complietion 配置，用于配置system prompt 和 schema

import type { Schema } from 'ai';
import { z } from 'zod';
import type { AIWriterType } from '@bika/types/ai/bo';
import { iStringRecordSchema } from '@bika/types/system';
import { type IAIWriterConfig, aiWriterConfigs } from '../client/ai/ai-writer/ai-writer';

// export function getAIWriterServerConfigs(): Record<AIWriterType, IAIWriterConfig> {

// }

export interface IAIWriterServerConfig<OBJECT extends object = object> extends IAIWriterConfig {
  schema?: z.Schema<OBJECT, z.ZodTypeDef, any> | Schema<OBJECT>;

  // 系统级prompt
  systemPrompt: string;
}

export const aiWriterServerConfigs: Record<AIWriterType, IAIWriterServerConfig> = {
  RESOURCE_README: {
    systemPrompt: '你将根据资源名称{xxx}和资源描述{xxx}和简要描述{xxx}，生成文件夹说明',
    schema: iStringRecordSchema, // z.object({ en: z.string() }), //
    ...aiWriterConfigs.RESOURCE_README,
    // type: 'OBJECT',
    // label: {
    //   'zh-CN': '生成资源说明',
    // },
    // description: '请提供资源的简要说明，以供 AI 参考',
    // systemPrompt: '你将根据资源名称{xxx}和资源描述{xxx}和简要描述{xxx}，生成文件夹说明',
    // showUserPrompt: true,
  },
  TEXT_GENERATION: {
    systemPrompt: '你是文案生成专家，根据描述生成独特文案',
    ...aiWriterConfigs.TEXT_GENERATION,
  },
  TEXT_REPHRASE: {
    systemPrompt: '你是文案润饰专家，通过优化内容生成更优质文案',
    ...aiWriterConfigs.TEXT_REPHRASE,
  },
  TRANSLATE: {
    systemPrompt: '你作为多国语言翻译专家，根据内容生成翻译文本',
    ...aiWriterConfigs.TRANSLATE,
  },
  I18N_STRING: {
    systemPrompt: '你作为多国语言翻译专家，生成符合 JSON 格式的翻译内容',
    ...aiWriterConfigs.I18N_STRING,
  },
  EXTRACT_TO_RECORD: {
    systemPrompt: '',
    ...aiWriterConfigs.EXTRACT_TO_RECORD,
  },
  RESOURCE_DESCRIPTION: {
    systemPrompt: '你是一个有用的文案创作工具。',
    ...aiWriterConfigs.RESOURCE_DESCRIPTION,
  },
  GENERATE_MOCK_RECORD: {
    systemPrompt: '',
    ...aiWriterConfigs.GENERATE_MOCK_RECORD,
  },
  RECORD_CELL_FILLING: {
    systemPrompt: '',
    ...aiWriterConfigs.RECORD_CELL_FILLING,
  },
  AI_MODEL_ACTION: {
    systemPrompt: '',
    ...aiWriterConfigs.AI_MODEL_ACTION,
  },
};
