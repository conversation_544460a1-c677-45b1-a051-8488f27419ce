import type { AIWriterType } from '@bika/types/ai/bo';
import type { iString } from '@bika/types/i18n/bo';
import { Locale } from '@bika/types/system';

export interface IAIWriterConfig {
  type: 'OBJECT' | 'TEXT';

  // 下拉框选择的文字
  label: iString;

  // helpText文字
  description?: iString;

  // UI界面是否可让用户输入提示
  showUserPrompt: boolean;

  // 是否默认给一个用户提示，预置prompt类似
  defaultUserPrompt?: string;
}

export interface IAIWriterContext {
  locale: Locale;
  createdAt: string;
  userId?: string;
  spaceId?: string;
}

export const aiWriterConfigs: Record<AIWriterType, IAIWriterConfig> = {
  TEXT_GENERATION: {
    type: 'OBJECT',
    label: {
      'zh-CN': '生成文案',
    },
    description: '请提供简要描述以供 AI 参考',
    showUserPrompt: true,
  },
  TEXT_REPHRASE: {
    type: 'OBJECT',
    label: {
      'zh-CN': '润饰文案',
    },
    description: '请提供简要描述以供 AI 参考',
    showUserPrompt: true,
  },
  TRANSLATE: {
    type: 'OBJECT',
    label: {
      'zh-CN': '文本翻译',
      'zh-TW': '文本翻譯',
      en: 'Text Translation',
      ja: 'テキスト翻訳',
    },
    description: {
      'zh-CN': '请在下方填写内容，AI 将协助你进行翻译。',
      'zh-TW': '請在下方填寫內容，AI 將協助你進行翻譯。',
      en: 'Please fill in the content below, AI will assist you in translation.',
      ja: '以下に内容を入力してください。AI が翻訳を支援します。',
    },
    showUserPrompt: true,
  },
  I18N_STRING: {
    type: 'OBJECT',
    label: '翻译多语言',
    description: '请在下方填写{xxx}，AI 将协助你翻译成简体中文、繁体中文、日语和英语',
    showUserPrompt: true,
  },
  EXTRACT_TO_RECORD: {
    type: 'OBJECT',
    label: {
      en: 'Extract Data',
      'zh-CN': '提取数据',
      'zh-TW': '提取數據',
      ja: 'データ抽出',
    },
    description: {
      en: 'Enter your content below, and the AI will extract key information to quickly create structured records. For better results, provide detailed and organized content. Currently, only text and numerical fields are supported.',
      'zh-CN':
        '在下方输入内容，AI 将提取关键信息，快速生成结构化记录。为获得更好的结果，请提供详细且有条理的内容。目前仅支持文本和数字类型的字段。',
      'zh-TW':
        '在下方輸入內容，AI 將提取關鍵信息，快速生成結構化記錄。為獲得更好的結果，請提供詳細且有條理的內容。目前僅支持文本和數字類型的字段。',
      ja: '以下にコンテンツを入力すると、AI がキー情報を抽出して迅速に構造化されたレコードを作成します。より良い結果を得るには、詳細で整理されたコンテンツを提供してください。現在、テキストと数値フィールドのみをサポートしています。',
    },
    showUserPrompt: true,
  },
  GENERATE_MOCK_RECORD: {
    type: 'OBJECT',
    label: {
      en: 'Generate Mock Record',
      'zh-CN': '生成示例数据',
      'zh-TW': '生成示例數據',
      ja: 'モックレコード生成',
    },
    description: {
      'zh-CN': 'AI 将分析数据表的所有字段，并根据字段类型和字段名称生成示例数据。',
      'zh-TW': 'AI 將分析數據表的所有字段，並根據字段類型和字段名稱生成示例數據。',
      en: 'AI will analyze all fields of the database and generate example data based on the field type and field name.',
      ja: 'AI はデータベースのすべてのフィールドを分析し、フィールドのタイプとフィールド名に基づいて例のデータを生成します。',
    },
    showUserPrompt: false,
  },
  RECORD_CELL_FILLING: {
    type: 'OBJECT',
    label: {
      'zh-CN': 'AI 填充记录单元格',
    },
    description: '请在下方填写内容，AI 将协助你填充记录单元格',
    showUserPrompt: true,
  },
  RESOURCE_DESCRIPTION: {
    type: 'OBJECT',
    label: {
      'zh-CN': 'AI 生成资源描述',
      'zh-TW': 'AI 生成資源描述',
      en: 'AI Generate Resource Description',
      ja: 'AI 生成リソースの説明',
    },
    description: {
      'zh-CN':
        '在下方填写关于资源的补充信息，AI 将根据上下文（如果是文件夹，将读取文件夹下的文件信息；如果是自动化，将读取触发器和动作信息），智能生成资源描述。',
      'zh-TW':
        '在下方填寫關於資源的補充信息，AI 將根據上下文（如果是資料夾，將讀取資料夾下的檔案資訊；如果是自動化，將讀取觸發器和動作資訊），智能生成資源描述。',
      en: 'Fill in the additional information about the resource below, AI will generate a resource description intelligently based on the context (if it is a folder, it will read the file information under the folder; if it is automation, it will read the trigger and action information).',
      ja: 'リソースに関する追加情報を以下に入力してください。AI はコンテキストに基づいてリソースの説明を知能的に生成します（フォルダの場合、フォルダ内のファイル情報を読み取ります。自動化の場合、トリガーおよびアクション情報を読み取ります）。',
    },
    showUserPrompt: true,
  },
  RESOURCE_README: {
    type: 'OBJECT',
    label: {
      'zh-CN': '生成资源说明',
    },
    description: '请提供资源的简要说明，以供 AI 参考',
    showUserPrompt: true,
  },
  AI_MODEL_ACTION: {
    type: 'OBJECT',
    label: {
      'zh-CN': 'AI 模型动作',
    },
    description: '请提供 AI 模型动作的简要说明，以供 AI 参考',
    showUserPrompt: true,
  },
};
